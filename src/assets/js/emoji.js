const emojiMap = new Map([
  [['[右哼哼]'], 0],
  [['[奸笑]'], 1],
  [['[红包]'], 2],
  [['[生病]'], 3],
  [['[囧]'], 4],
  [['[惊恐]'], 5],
  [['[Emm]'], 6],
  [['[得意]'], 7],
  [['[菜刀]'], 8],
  [['[太阳]'], 9],
  [['[强壮]'], 10],
  [['[捂脸]'], 11],
  [['[机智]'], 12],
  [['[耶]'], 13],
  [['[尴尬]'], 14],
  [['[抓狂]'], 15],
  [['[流汗]'], 16],
  [['[社会社会]'], 17],
  [['[擦汗]'], 18],
  [['[西瓜]'], 19],
  [['[拥抱]'], 20],
  [['[破涕为笑]'], 21],
  [['[皱眉]'], 22],
  [['[小鸡]'], 23],
  [['[微笑]'], 24],
  [['[发怒]'], 25],
  [['[吐]'], 26],
  [['[憨笑]'], 27],
  [['[旺柴]'], 28],
  [['[抠鼻]'], 29],
  [['[啤酒]'], 30],
  [['[强]'], 31],
  [['[笑脸]'], 32],
  [['[调皮]'], 33],
  [['[呲牙]'], 34],
  [['[惊讶]'], 35],
  [['[难过]'], 36],
  [['[色]'], 37],
  [['[悠闲]'], 38],
  [['[疑问]'], 39],
  [['[鼓掌]'], 40],
  [['[害羞]'], 41],
  [['[睡觉]'], 42],
  [['[无语]'], 43],
  [['[偷笑]'], 44],
  [['[愉快]'], 45],
  [['[白眼]'], 46],
  [['[傲慢]'], 47],
  [['[困]'], 48],
  [['[发呆]'], 49],
  [['[好的]'], 50],
  [['[坏笑]'], 51],
  [['[咖啡]'], 52],
  [['[弱]'], 53],
  [['[失望]'], 54],
  [['[奋斗]'], 55],
  [['[咒骂]'], 56],
  [['[吃瓜]'], 57],
  [['[加油]'], 58],
  [['[汗]'], 59],
  [['[天啊]'], 60],
  [['[打脸]'], 61],
  [['[左哼哼]'], 62],
  [['[吃饭]'], 63],
  [['[握手]'], 64],
  [['[吐舌]'], 65],
  [['[哇]'], 66],
  [['[嘘]'], 67],
  [['[晕]'], 68],
  [['[衰]'], 69],
  [['[骷髅]'], 70],
  [['[敲打]'], 71],
  [['[再见]'], 72],
  [['[嘿哈]'], 73],
  [['[猪头]'], 74],
  [['[胜利]'], 75],
  [['[恐惧]'], 76],
  [['[哈欠]'], 77],
  [['[鄙视]'], 78],
  [['[委屈]'], 79],
  [['[流泪]'], 80],
  [['[快哭了]'], 81],
  [['[阴险]'], 82],
  [['[亲亲]'], 83],
  [['[可怜]'], 84],
  [['[玫瑰]'], 85],
  [['[抱拳]'], 86],
  [['[脸红]'], 87],
  [['[凋谢]'], 88],
  [['[嘴唇]'], 89],
  [['[爱心]'], 90],
  [['[心碎]'], 91],
  [['[蛋糕]'], 92],
  [['[闭嘴]'], 93],
  [['[炸弹]'], 94],
  [['[便便]'], 95],
  [['[月亮]'], 96],
  [['[勾引]'], 97],
  [['[合十]'], 98],
  [['[拳头]'], 99],
  [['[OK]'], 100],
  [['[大哭]'], 101],
  [['[跳跳]'], 102],
  [['[发抖]'], 103],
  [['[怄火]'], 104],
  [['[转圈]'], 105],
  [['[礼物]'], 106],
  [['[庆祝]'], 107],
  [['[鬼混]'], 108],
  [['[撇嘴]'], 109]
])

/**
 * @param {String} str emoji文字消息，如"[微笑]"
 */
export function indexOfEmoji (str) {
  let idx = -1
  emojiMap.forEach((val, key) => {
    if (key.includes(str)) {
      idx = val
    }
  })
  return idx
}

export default emojiMap
