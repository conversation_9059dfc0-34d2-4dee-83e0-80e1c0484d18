.chat-photo-select__container {
  position: relative;
  display: flex;
  justify-content: space-between;
  height: 200px;
  padding: 20px 20px;
  box-sizing: border-box;
  >.chat-photo-select__item {
    position: relative;
    display: flex;
    align-items: center;
    align-content: center;
    flex-direction: column;
    .chat-photo-select__file {
      position: absolute;
      width: 50px;
      height: 50px;
      top: 0;
      left: 0;
      opacity: 0;
    }
    .chat-photo-select__icon {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 50px;
      height: 50px;
      border-radius: 10px;
      background: #fff;
      &:active {
        background: #ccc;
      }
    }
    .chat-photo-select__title {
      margin-top: 5px;
      font-size: 12px;
    }
  }
  .chat-photo-select__item + .chat-photo-select__item {
    margin-left: 30px;
  }
}