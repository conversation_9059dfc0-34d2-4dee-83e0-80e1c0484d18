@import './mixin.less';

.im-layout__container {
  position: absolute;
  width: 100%;
  top: 0;
  bottom: 0;
  overflow-x: hidden;
  .fit-ios-padding-top();
  .fit-ios-padding-bottom();
}

.fade-left-enter-active {
  animation: fadeInLeft .3s ease;
  animation-fill-mode: both;
}
.fade-left-leave-active {
  animation: fadeOutLeft .3s ease;
  animation-fill-mode: both;
}
.fade-right-enter-active {
  animation: fadeInRight .3s ease;
  animation-fill-mode: both;
}
.fade-right-leave-active {
  animation: fadeOutRight .3s ease;
  animation-fill-mode: both;
}