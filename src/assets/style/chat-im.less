.chat-im__container {
  width: 100%;
  position: absolute;
  top: 46px;
  bottom: 0;
  display: flex;
  flex-direction: column;
  background: #f7f8fa;
  >.chat-im__scroller {
    flex: 1;
  }
  .chat-im__list-layout {
    box-sizing: border-box;
    width: 100%;
    margin: 0;
    transition: padding .3s;
    padding: 0 10px 0 10px;
    // > .chat-im-nav{
    //   display: flex;
    //   justify-content: space-around;
    //   align-items: center;
    //   padding: .48rem 0; /* 18/37.5 */
    //   background-image: url('../img/navBg.png');
    //   background-size: cover;
    //   background-position: center;
    //   background-repeat: no-repeat;
    //   border-radius: 10px;
    //   position: relative;
    //     &::before {
    //       content: '';
    //       display: inline-block;
    //       width: .853333rem; // 图标的宽度
    //       height: .853333rem; // 图标的高度
    //       left: 50%;
    //       top: -0.32rem /* 12/37.5 */;
    //       transform: translateX(-50%);
    //       background-image: url('../img/logo.png'); // 替换为图标的路径
    //       background-size: contain; // 根据需要设置
    //       background-repeat: no-repeat;
    //       margin-right: .266667rem /* 10/37.5 */; // 与文本的间距
    //       position: absolute;
    //     }
    //   > .chat-im-nav-box {
    //     text-align: center;
    //     text-decoration: none;
    //     width: 1.866667rem; /* 70/37.5 */
    //     // height: 1.546667rem; /* 58/37.5 */
    //     text-align: center;
    //     vertical-align: middle;
    //     > img {
    //       width: .906667rem!important; /* 34/37.5 */
    //       height: .906667rem!important; /* 34/37.5 */
    //     }
    //     > span {
    //       display: block;
    //       margin-top: .133333rem; /* 5/37.5 */
    //       font-size: .32rem !important; /* 12/37.5 */
    //     }
    //   } 
    // }
    > .question-box {
      background-color: #fff;
      margin-top: 8px;
      border-radius: 10px;
      > .question-title {
        color: #1D2129;
        font-size: .48rem /* 18/37.5 */;
        line-height: .693333rem /* 26/37.5 */;
        font-weight: 500;
        padding: .133333rem /* 5/37.5 */ 0 0 .426667rem;  /* 16/37.5 */
      }
      ul {
        //  color: '#1D2129';
         padding-left: .426667rem; /* 16/37.5 */
         padding-right: .426667rem; /* 16/37.5 */
         li {
          padding: .293333rem 0; /* 11/37.5 */
          border-bottom: 1px solid rgba(169, 169, 169, 0.804);
          display: grid;
          align-items: center;
          justify-items: start;
          span {
            // line-height: .48rem /* 18/37.5 */;
            // line-height: .346667rem; /* 13/37.5 */
            line-height: .5rem;
            font-size: .346667rem; /* 13/37.5 */
          }
        }
      }
      .question-more {
        font-size: .32rem /* 12/37.5 */;
        color: #86909C;
        text-align: center;
        padding: .293333rem /* 11/37.5 */;
      }
    }
    > .chat-im__list-view {
      list-style: none;
      padding: 0;
      position: relative;
      overflow: auto;
    }
    > .chat-im__list-view:last-child /deep/ .chat-message-item__all-content{
      margin-bottom: 10px;
    }
  }
}