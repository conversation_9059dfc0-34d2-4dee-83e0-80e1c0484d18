.chat-emoji-select__container {
  position: relative;
  >.chat-emoji-select__backspace {
    position: absolute;
    bottom: 20px;
    right: 10px;
    width: 60px;
    height: 40px;
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    i {
      font-size: 28px;
    }
  }
  >.chat-emoji-select__wrapper {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    width: 100%;
    height: 200px;
    padding: 10px;
    box-sizing: border-box;
    background: #eee;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
    & > i{
      width: 30px;
      margin: 5px;
    }
    &::-webkit-scrollbar {
      display: none;
    }
  }
}
.chat-emoji-select__icon {
  display: flex;
  width: 29px;
  height: 29px;
  margin: 5px;
  padding: 2px;
  border-radius: 4px;
  background: url('~@/assets/img/emoji.png');
  background-size: 1100% 1000%;
  background-repeat: no-repeat;
  transition: transform .1s;
  &:active {
    transform: scale(1.2);
  }
}