@import './mixin.less';

.room-im__container {
  width: 100%;
  position: absolute;
  top: 0;
  bottom: 50px;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
  padding-right: 10px;
  user-select: none;
  .fit-ios-padding-bottom();
  &::-webkit-scrollbar {
    display: none;
  }
}
/deep/ .im-card__container:nth-last-child(1) >.im-card__right::after {
  border-bottom: 0px solid #ebedf0;
}
.isTop {
  background-color: #f5f5f5 !important;
}