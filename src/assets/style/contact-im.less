@import './mixin.less';
@import './var.less';

.contact-im__container {
  width: 100%;
  position: absolute;
  top: 0;
  bottom: 50px;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
  padding-right: 10px;
  user-select: none;
  background: #f7f8fa;
  .fit-ios-padding-bottom();
  &::-webkit-scrollbar {
    display: none;
  }
  .contact-im__add-friend {
    align-items: center;
    &::after {
      border: 0px;
    }
  }
  .contact-im__cell-item {
    align-items: center;
    .van-icon__image {
      width: 24px;
      height: 24px;
      object-fit: fill;
      border-radius: 4px;
    }
    &:active {
      background: @bg-gray-active
    }
  }
  /deep/ .van-index-anchor--sticky, /deep/ .van-index-bar__index--active {
    color: @theme-color;
  }
}