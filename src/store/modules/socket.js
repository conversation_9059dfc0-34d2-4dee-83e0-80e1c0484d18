
import io from 'socket.io-client'
import cons from '@/constants'
import Bus from '@/assets/js/bus'
import api from '@/api'

const state = () => ({
  socket: null,
  socketSuccess: false, // 初始化socketError为false
  sessionId: '',
  agentId: ''
})

const mutations = {
  SET_SOCKET (state, socket) {
    state.socket = socket
  },
  SET_SOCKET_SUCCESS (state, error) {
    state.socketSuccess = error
  },
  SET_SESSION_ID (state, sessionId) {
    state.sessionId = sessionId
  },
  SET_AGENT_ID (state, agentId) {
    state.agentId = agentId
  }
}

const actions = {
  createSocket ({ dispatch, commit, state, rootGetters }, data) {
    const socketio = io(cons.url.PREFIX, {
      transports: ['websocket', 'polling'],
      path: '/poc-room.io',
      query: {
        // isRegister: (data && data.isRegister) || false, // 是否第一次注册,服务端连接成功后发送一条注册成功消息
        id: rootGetters.userId || 'admin',
        type: 1
      },
      reconnection: true,
      reconnectionDelay: 3000,
      reconnectionAttempts: 8
    })
    socketio.on('connect', () => {
      console.log('socket连接状态success ')
      commit('SET_SOCKET_SUCCESS', true) // 设置socketError为true
      socketio.emit('customInit', {
        id: rootGetters.userId || 'admin',
        from: rootGetters.userId || 'admin',
        type: 'init'
      }, (response) => {
        console.log('收到socket消息', response)
        if (response && response.sessionId) {
          console.log('收到的sessionId:', response.sessionId)
        }
      })
    })
    socketio.on('event', (data, callback) => {
      console.log('收到socket消息', data)
      switch (data.type) {
        case 0: {
          if (data.cmd === 1) { // 欢迎语
            const item = {
              lastMsgTime: data.sendTime,
              lastMsg: data.content,
              name: data.from,
              nickname: data.nickname,
              isTop: 0,
              unReadNum: 1
            }
            dispatch('room/addRoom', { item: item }, { root: true })
          } else if (data.cmd === 2) { // 加人成功消息
            const { roomId, lastMsgTime, lastMsg, name } = data
            const item = {
              roomId,
              lastMsgTime,
              lastMsg,
              name,
              nickname: null,
              isTop: 0,
              unReadNum: 1
            }
            if (rootGetters.roomList.some(val => val.roomId === item.roomId)) {
              dispatch('room/updateLastMsg', { roomId, lastMsg, lastMsgTime, name, unReadNum: 1 }, { root: true })
            } else {
              dispatch('room/addRoom', { item: item }, { root: true })
            }
            dispatch('room/removeExpiredRoomIdList', roomId, { root: true })
          } else if (data.cmd === 3) { // 删除好友成功消息
            const { roomId } = data
            dispatch('room/addExpiredRoomIdList', roomId, { root: true })
          }
          break
        }
        case 'text':
        case '1':
        case 1: {
          if (data.sendBy && data.sendBy === '1') {
            commit('SET_AGENT_ID', data.from) // 设置sessionId
          }
          Bus.$emit('accept-socket-message', data)
          break // 文本消息
        }
        case 2:
        case '2':
        case 'image':
          Bus.$emit('accept-socket-message', data) // 图片消息
          break
        case 5:
        case '5':
        case 'file':
          Bus.$emit('accept-socket-message', data) // 文件消息
          break
        case 'init':
          commit('SET_SESSION_ID', data.sessionId) // 设置sessionId
          break
        case 'exit':
          Bus.$emit('accept-socket-message', data) // 离开时发送消息
          dispatch('closeSession')
          break
      }

      const callbackFunction = callback
      callbackFunction({ status: 200 })
    })
    socketio.on('connect_error', () => {
      console.log('socket连接状态connect_error :>> ')
    })
    socketio.on('disconnect', (data) => {
      console.log('socket连接状态disconnect :>> ')
    })
    socketio.on('connect_timeout', () => {
      console.log('socket连接状态connect_timeout :>> ')
    })
    socketio.on('reconnect', () => {
      console.log('socket连接状态reconnect :>> ')
    })
    socketio.on('reconnect_attempt', () => {
      console.log('socket连接状态reconnect_attempt :>> ')
    })
    socketio.on('reconnecting', () => {
      console.log('socket连接状态reconnecting :>> ')
    })
    socketio.on('reconnect_error', () => {
      console.log('socket连接状态reconnect_error :>> ')
    })
    socketio.on('reconnect_failed', () => {
      console.log('socket连接状态reconnect_failed :>> ')
    })
    socketio.on('error', (error) => {
      console.error('Socket 错误:', error)
      // 你可以在这里添加更多逻辑，比如尝试重新连接
    })
    socketio.on('exit', () => {
      dispatch('closeSession')
    })
    commit('SET_SOCKET', socketio)
  },

  // 新增发送消息的 action  一定要在创建连接之前
  sendMessage ({ state }, messageData) {
    if (state.socket) {
      state.socket.emit('transferMsg', messageData)
    } else {
      console.error('Socket 未初始化')
    }
  },

  closeSession ({ state, commit }) {
    if (state.socket) {
      // 发送关闭 session 的 emit
      api.colseLine({ sessionId: state.sessionId }).then(res => {
        if (res.data.success === true) {
          // 可选：断开 socket 连接
          state.socket.disconnect()
          // 重置 socket 状态
          commit('SET_SOCKET', null)
          commit('SET_SESSION_ID', '') // 这里已经将 sessionId 制空
          commit('SET_AGENT_ID', '') // 这里已经将 agentId 制空
          commit('SET_SOCKET_SUCCESS', false)
        }
      })
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
