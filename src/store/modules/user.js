import api from '@/api'
// import cons from '@/constants'

const state = () => ({
  username: '',
  avatarUrl: '',
  nickName: '',
  userId: '',
  roomId: ''
})

const mutations = {
  SET_USER_NAME (state, username) {
    state.username = username || ''
  },
  SET_AVATAR_URL (state, avatarUrl) {
    state.avatarUrl = avatarUrl || ''
  },
  SET_NICK_NAME (state, nickName) {
    state.nickName = nickName || ''
  },
  SET_USER_ID (state, userId) {
    state.userId = userId || ''
  },
  SET_DEPT_NAME (state, deptName) {
    state.deptName = deptName || ''
  },
  SET_ROOM_ID (state, roomId) {
    state.roomId = roomId || ''
  }
}

const actions = {
  getUserInfo ({ commit }, data) {
    return new Promise(function (resolve, reject) {
      api.getUserInfo(data).then(res => {
        if (res.data.success) {
          const { name, userid, deptName } = res.data.data
          console.log(res.data.data)
          commit('SET_USER_NAME', userid)
          commit('SET_NICK_NAME', name)
          commit('SET_USER_ID', userid)
          commit('SET_DEPT_NAME', deptName)
          resolve(res.data.data)
        } else {
          reject(new Error(res.data.errmsg))
        }
      }).catch(rej => {
        console.log(rej)
        reject(new Error('系统异常'))
      })
    })
  },
  getRoomId ({ commit }, data) {
    return new Promise(function (resolve, reject) {
      api.getRoomId(data).then(res => {
        if (res.data.success) {
          // const roomId = res.data.data
          commit('SET_ROOM_ID', res.data.data.roomId)
          resolve(res.data.data)
        } else {
          reject(new Error(res.data.errmsg))
        }
      }).catch(rej => {
        console.log(rej)
        reject(new Error('系统异常'))
      })
    })
  }
  // logout ({ commit, rootGetters }) {
  //   localStorage.removeItem(cons.TOKEN_KEY)
  //   localStorage.removeItem('vuex')
  //   rootGetters.socket.close()
  // }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
