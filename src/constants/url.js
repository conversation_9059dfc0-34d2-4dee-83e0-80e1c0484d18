const prefixUrl = ''

if (__STG__ || __PRD__) {
  // prefixUrl = 'http://your.hostname.com'
}

export default {
  PREFIX: prefixUrl, // url前缀
  LOGIN: prefixUrl + '/api/login/index', // 登录接口url
  SEND: prefixUrl + '/api/chat/send', // 发消息接口url
  GET_MSG_LIST: prefixUrl + '/api/chat/getMsgList', // 获取聊天记录url
  SEARCH_USER: prefixUrl + '/api/user/search', // 搜索用户url
  ADD_FRIEND: prefixUrl + '/api/user/addFriend', // 添加好友
  DELETE_FRIEND: prefixUrl + '/api/user/deleteFriend', // 删除好友
  GET_ADD_LIST: prefixUrl + '/api/user/addList', // 获取申请好友列表
  AGREE_ADD: prefixUrl + '/api/user/agreeAdd', // 同意申请
  DELETE_ADD_INFO: prefixUrl + '/api/user/deleteAddInfo', // 删除申请信息
  GET_FRIEND_LIST: prefixUrl + '/api/user/getFriendList', // 获取好友列表
  UPLOAD_IMG: prefixUrl + '/minioServer/file/upload', // 上传图片url
  SEND_TO_ROBOT: prefixUrl + '/airChinaHelp/robotChat/chat', // 发送消息给机器人
  GUESS_LIST: prefixUrl + '/airChinaHelp/hotQuestion/getList', // 猜你喜欢列表
  GET_USER_INFO: prefixUrl + '/airChinaHelp/ewxim/getUserInfo', // 获取用户信息
  GET_ROOM_ID: prefixUrl + '/airChinaHelp/robotChat/getRoomId', // 获取roomId
  DISCONNECT_LINK: prefixUrl + '/airChinaHelp/closeSession', // 断开连接
  GET_CHAT_LIST: prefixUrl + '/airChinaHelp/getRecord', // 获取聊天列表
  GET_HISTORY_LIST: prefixUrl + '/airChinaHelp/getSessionHistory' // 获取历史消息列表
}
