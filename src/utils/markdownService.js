import MarkdownIt from 'markdown-it'

// 创建 markdown 解析器实例并配置
const md = new MarkdownIt({
  html: true, // 允许在源文件中使用 HTML 标签
  linkify: true, // 自动将 URL 文本转换为链接
  typographer: true, // 启用语言中立的替换和引号美化
  breaks: true // 将段落中的 '\n' 转换为 <br>
})

// 自定义链接渲染器(使链接在新窗口打开)
const defaultRender =
  md.renderer.rules.link_open ||
  function (tokens, idx, options, env, self) {
    return self.renderToken(tokens, idx, options)
  }

md.renderer.rules.link_open = function (tokens, idx, options, env, self) {
  tokens[idx].attrPush(['target', '_blank'])
  tokens[idx].attrPush(['rel', 'noopener noreferrer'])

  return defaultRender(tokens, idx, options, env, self)
}

/**
 * 将 markdown 内容渲染为 HTML
 * @param {string} content - 要渲染的 markdown 内容
 * @returns {string} 渲染后的 HTML
 */
export function renderMarkdown (content) {
  if (!content) return ''
  try {
    return md.render(content)
  } catch (err) {
    console.error('Markdown 渲染错误:', err)
    return content
  }
}

/**
 * 获取 markdown 解析器实例
 * @returns {MarkdownIt} markdown 解析器实例
 */
export function getMarkdownParser () {
  return md
}

export default {
  renderMarkdown,
  getMarkdownParser
}
