import Vue from 'vue'
import VueRouter from 'vue-router'
import getTerminalType from '../plugins/terminalType'

Vue.use(VueRouter)

const router = new VueRouter({
  routes: [
    // {
    //   path: '/im-layout',
    //   name: 'ImLayout',
    //   component: () => import('../components/im-layout'),
    //   children: [
    //     {
    //       path: '/im-layout/chat-im',
    //       name: 'ImChatClient',
    //       meta: {
    //         index: 2,
    //         backflag: false,
    //         tp: false,
    //         platform: 'mobile'
    //       },
    //       component: () => import('../views/imClient/chat-im.vue')
    //     }
    //   ]
    // },
    {
      path: '/im-layout',
      name: 'ImSimpleLayout',
      component: () => import('../components/im-simple-layout'),
      children: [
        {
          path: '/im-layout/h5-chat',
          name: 'ImChat',
          meta: {
            index: 2,
            backflag: false,
            tp: false,
            platform: 'mobile'
          },
          component: () => import('../views/imChat/index.vue')
        }
      ]
    },
    {
      path: '/im-web-layout',
      name: 'ImWebLayout',
      component: () => import('../components/im-web-layout'),
      children: [
        {
          path: '/im-web-layout/chat-im-pc',
          name: 'ImChatPc',
          meta: {
            index: 2,
            backflag: false,
            tp: true,
            platform: 'pc'
          },
          component: () => import('../views/imPC/chat-im-pc.vue')
        }
      ]
    },
    {
      path: '/',
      redirect: () => {
        const terminalType = getTerminalType()
        return terminalType === 'mobile'
          ? '/im-layout/h5-chat'
          : '/im-web-layout/chat-im-pc'
      }
    },
    {
      path: '*',
      redirect: () => {
        const terminalType = getTerminalType()
        return terminalType === 'mobile'
          ? '/im-layout/h5-chat'
          : '/im-web-layout/chat-im-pc'
      }
    }
  ]
})

router.beforeEach((to, from, next) => {
  // 获取终端类型
  const terminalType = getTerminalType()
  if (to.meta.platform === 'mobile' && terminalType === 'pc') {
    next('/im-web-layout/chat-im-pc')
  } else if (to.meta.platform === 'pc' && terminalType === 'mobile') {
    next('/im-layout/h5-chat')
  } else {
    next()
  }
})

export default router
