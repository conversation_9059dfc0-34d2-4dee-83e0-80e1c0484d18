import request from '@/assets/js/request'
import cons from '@/constants'

export default {
  uploadImg (data) { // 上传图片接口
    return request.post(cons.url.UPLOAD_IMG, data)
  },
  send (data) { // 发消息接口
    return request.post(cons.url.SEND, data)
  },
  getMsgList (data) { // 获取聊天记录
    return request.post(cons.url.GET_MSG_LIST, data)
  },
  sendToRobot (data) { // 发送给机器人
    return request.post(cons.url.SEND_TO_ROBOT, data)
  },
  guessList (data) { // 获取猜词列表
    return request.post(cons.url.GUESS_LIST, data)
  },
  getUserInfo (query) { // 获取用户信息
    // return request({ method: 'post', url: cons.url.GET_USER_INFO, data: {}, params: query }) 如果需要formate中也有值的话
    return request({ method: 'post', url: cons.url.GET_USER_INFO, params: query })
  },
  colseLine (query) { // 关闭线路
    return request({ method: 'post', url: cons.url.DISCONNECT_LINK, params: query })
  },
  getRoomId (data) {
    return request.post(cons.url.GET_ROOM_ID, data)
  },
  getChatList (query) { // 获取聊天信息列表
    return request({ method: 'post', url: cons.url.GET_CHAT_LIST, params: query })
  },
  getHistoryChart (data) { // 获取历史记录
    return request.post(cons.url.GET_HISTORY_LIST, data)
  }
}
