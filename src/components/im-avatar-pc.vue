<template>
    <div>
        <el-avatar
            v-if="url"
            shape="square"
            :size="size"
            :src="url"
            fit="cover"
            ></el-avatar>
        <el-avatar v-if="!url" shape="square" :size="size" fit="cover">
        <template slot="default">
            <span style="font-size: 2rem">{{ start }}</span>
        </template>
        </el-avatar>
    </div>
  </template>

<script>
// import FetchRequest from '@/api/FetchRequest'

export default {
  props: {
    size: {
      type: String,
      required: false,
      default: 'default'
    },
    img: {
      type: String,
      required: true,
      default: '@/assets/icon.png'
    },
    name: {
      type: String,
      required: false,
      default: ''
    }
  },
  data () {
    return {
    //   host: FetchRequest.getHost()
    }
  },
  computed: {
    url () {
      if (this.img?.indexOf('http') > -1) {
        return this.img
      } else if (this.img?.trim() === '') {
        return null
      } else {
        return this.host + this.img
      }
    },
    start () {
      return this.name ? this.name[0] : ''
    }
  }
}
</script>

  <style lang="less" scoped>
  .avatar {
    width: 6rem;
    height: 6rem;
  }
  </style>
