<template>
  <div class="markdown-viewer" v-html="renderedContent"></div>
</template>

<script>
import { renderMarkdown } from '@/utils/markdownService'

export default {
  name: 'MarkdownViewer',
  props: {
    content: {
      type: String,
      default: ''
    },
    needFormat: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    renderedContent () {
      const content = this.content
      if (this.needFormat) {
        let formattedContent = this.formatDataToMarkdown(content)
        formattedContent = renderMarkdown(formattedContent)
        return formattedContent
      } else {
        return renderMarkdown(content)
      }
    }
  },
  methods: {
    formatDataToMarkdown (rawData) {
      if (!rawData) return ''

      const formatted = rawData
        // 处理连在一起的标题：在文字和#之间添加换行
        .replace(/([^#\n])(#{1,6})/g, '$1\n$2')
        // 处理标题层级：确保所有#前都有换行
        .replace(/^(#{1,6})/g, '\n$1')
        // 清理开头的换行
        .replace(/^\n+/, '')
        // 标准化标题格式：确保#后有空格，但不拆分连续的#
        .replace(/(#{1,6})([^#\s])/g, '$1 $2')
        // 确保标题后有换行
        .replace(/(#{1,6}\s[^\n]*?)(?=\n|$)/g, '$1\n')

        // 先处理标题中的：**格式，去除空格：### 标题：** 内容** -> ### 标题：**内容**
        .replace(/(#{1,6}\s[^*\n]*?[：:])\s+(\*\*[^*]+\*\*)/g, '$1$2')

        // 处理标题后直接跟粗体文本：### 标题**粗体** -> ### 标题\n\n**粗体**
        .replace(/(#{1,6}\s[^*\n]*?)(\*\*[^*]+\*\*)/g, '$1\n$2')

        // **- ** 转换为 **\n- **
        .replace(/\*\*-/g, '**\n- ')
        // 字符XX- ** 转换为 \nXX- **
        .replace(/(\d+)- \*\*/g, '$1\n- **')
        // 处理中文文字后直接跟- **的情况：文字- ** 转换为 文字\n- **
        .replace(/([\u4e00-\u9fff]+)- \*\*/g, '$1\n- **')
        // 处理英文/符号后直接跟- **的情况：）- ** 转换为 ）\n- **
        // eslint-disable-next-line no-useless-escape
        .replace(/([）\)])- \*\*/g, '$1\n- **')
        // 处理冒号后直接跟-的情况：： - 转换为 ：\n-
        .replace(/([：:]) -/g, '$1\n- ')

        // 处理编号列表：确保数字前有换行，但数字后不换行
        .replace(/([^\n])(\d+\.\s*\*\*)/g, '$1\n$2')

        // 处理连续编号列表：1. xxx2. xxx -> 1. xxx\n2. xxx
        .replace(/(\d+\.\s*\*\*[^*]+\*\*[：:][^0-9]*?)(\d+\.)/g, '$1\n$2')
        // 处理标题后直接跟有序列表的情况：###标题：1.内容 -> ###标题：\n1. 内容
        .replace(/(#{1,6}\s[^:\n]*[：:])(\d+\.)/g, '$1\n$2')
        // 处理粗体标题后直接跟有序列表的情况：**标题**：1.内容 -> **标题**：\n1. 内容
        .replace(/(\*\*[^*]+\*\*[：:])(\d+\.)/g, '$1\n$2')
        // 处理连续的有序列表项：1.内容2.内容 -> 1. 内容\n2. 内容
        .replace(/(\d+\.)([^0-9\n]*?)(\d+\.)/g, '$1 $2\n$3')
        // 处理更复杂的有序列表连接：1. xxx。2. xxx -> 1. xxx。\n2. xxx
        .replace(/(\d+\.\s+[^0-9\n]*?[。！？])(\d+\.)/g, '$1\n$2')
        // 处理连续有序列表项（任何结尾）：1. xxx2. xxx -> 1. xxx\n2. xxx
        .replace(/(\d+\.\s+[^0-9]*?)(\d+\.)/g, '$1\n$2')
        // 处理连续数字后直接跟数字的特殊情况：xxx。2. -> xxx。\n2.
        .replace(/([。！？])(\d+\.)/g, '$1\n$2')
        // 处理更通用的连续有序列表：number.content3.content -> number.content\n3.content
        .replace(/(\d+\.[^0-9\n][^0-9]*?)(\d+\.)/g, '$1\n$2')
        // 强化处理：确保任何数字后面跟着的数字序号都换行
        .replace(/([。！？\s])(\d+\.\s)/g, '$1\n$2')
        // 处理连续的有序列表项（句号结尾）：1. xxx。2. xxx -> 1. xxx。\n2. xxx
        .replace(/(\d+\.\s[^0-9]*?[。！？])\s*(\d+\.)/g, '$1\n$2')
        // 确保有序列表项有正确的空格：1.内容 -> 1. 内容，但不处理URL中的内容
        .replace(/(\d+\.)([^\s])/g, function (match, p1, p2, offset, string) {
          // 检查是否在markdown链接的URL部分（圆括号内）
          const beforeMatch = string.substring(0, offset)
          const lastOpenParen = beforeMatch.lastIndexOf('](')
          const lastCloseParen = beforeMatch.lastIndexOf(')')
          // 如果在](之后且没有闭合的)，说明在URL中，不进行替换
          if (lastOpenParen !== -1 && lastOpenParen > lastCloseParen) {
            return match
          }
          return p1 + ' ' + p2
        })
        // 确保减号列表项有正确的空格：-内容 -> - 内容
        .replace(/^-([^\s])/gm, '- $1')
        .replace(/(\n)-([^\s])/g, '$1- $2')

        // 处理各种列表项标记
        .replace(/([。！？：:])(\s*-)([^-])/g, '$1\n- $3')

        // 处理连续的列表项：- xxx- xxx -> - xxx\n- xxx
        .replace(/(-\s*\*\*[^*]+\*\*[：:][^-]*?)(-\s*\*\*)/g, '$1\n$2')

        // 处理非标题情况下的：**格式，添加空格便于阅读：文字：**内容** -> 文字：** 内容**
        .replace(/([^#\n][\u4e00-\u9fff]+)[：:]\*\*/g, '$1：** ')

        // 处理嵌套列表项：空格+减号的情况
        .replace(/(\s+)-(\s*\*\*[^*]+\*\*[：:])/g, '\n$1- $2')

        // 处理连续的粗体文本：**文字****文字** 转换为 **文字**\n**文字**
        // 但排除 **xxx**：**yyy** 这种带冒号的情况
        .replace(/(\*\*[^*]+\*\*)(?![：:])\s*(\*\*[^*]+\*\*)/g, '$1\n$2')

        // 处理连续粗体标题直接相连：**标题1****标题2** -> **标题1**\n**标题2**
        .replace(/(\*\*[^*]+\*\*)(\*\*[^*]+\*\*)/g, '$1\n$2')

        // 处理连续的粗体标题格式：**标题**：内容**标题**：内容 -> **标题**：内容\n**标题**：内容
        .replace(
          /(\*\*[^*]+\*\*[：:])([^*]*?)(\*\*[^*]+\*\*[：:])/g,
          '$1$2\n$3'
        )

        // 继续处理可能遗漏的连续粗体标题（多次执行以确保完全处理）
        .replace(
          /(\*\*[^*]+\*\*[：:])([^*]*?)(\*\*[^*]+\*\*[：:])/g,
          '$1$2\n$3'
        )
        .replace(
          /(\*\*[^*]+\*\*[：:])([^*]*?)(\*\*[^*]+\*\*[：:])/g,
          '$1$2\n$3'
        )

        // 确保每个粗体标题前都有换行（除了开头和序号后）
        // eslint-disable-next-line no-useless-escape
        .replace(/([^\n\d\.])(\*\*[^*]+\*\*[：:])/g, '$1\n$2')

        // 处理列表项中的连续内容：确保每个-项前有换行
        .replace(/([^-\n])\s*-\s*(\*\*[^*]+\*\*[：:])/g, '$1\n- $2')

        // 处理空格后跟减号的列表项
        .replace(/(\s+)-\s*(\*\*[^*]+\*\*[：:])/g, '\n$1- $2')

        // 处理冒号后直接跟内容的情况：添加适当换行
        .replace(/([：:])\s*(\d+\.\s*\*\*)/g, '$1\n$2')

        // 修复错误的序号换行：确保序号和内容在同一行
        .replace(/(\d+\.)\n(\s*\*\*[^*]+\*\*[：:])/g, '$1 $2')

        // 最后确保序号格式正确：移除序号后的多余换行和空格
        .replace(/(\d+\.)[\s\n]+(\*\*[^*]+\*\*[：:])/g, '$1 $2')

        // 特殊中文直接匹配中文
        // eslint-disable-next-line no-useless-escape
        .replace(/\*\*总体评价\*\*/g, '\n\*\*总体评价\*\*')
        // eslint-disable-next-line no-useless-escape
        .replace(/\*\*注\*\*/g, '\n\*\*注\*\*')

        // 表格处理
        // 1. 处理||到换行的转换
        .replace(/\|\|/g, '|\n|')
        // 2. 处理标题紧连表格的情况：标题|表格 -> 标题\n\n|表格
        .replace(/(#{1,6}\s[^|\n]*)\|/g, '$1\n\n|')
        // 3. 修复被拆分的表格分隔线：识别并重新组合
        .replace(
          /\|\s*\n\s*(-+[^|\n]*)\n\s*(-+[^|\n]*)\|/g,
          function (match, part1, part2) {
            return '|' + part1 + '|' + part2 + '|'
          }
        )

        // 处理分隔线---：但要避免处理表格分隔线
        .replace(/([^\n|-])---([^\n|-])/g, '$1\n---\n\n$2')
        .replace(/^---([^\n|-])/gm, '---\n\n$1')
        .replace(/([^\n|-])---$/gm, '$1\n---')
        .replace(/^---$/gm, '---\n')

        // 处理特殊符号和标点
        .replace(/·([^·\n]+)/g, '\n- $1')
        .replace(/；([^；\n]+)/g, ';\n$1')

        // 处理紧凑的列表格式：确保列表项之间有适当间距
        .replace(/(\*\*[^*]+\*\*[：:][^-\n]*?)(\s*-\s*\*\*)/g, '$1\n$2')

        // 修复数字编号后直接跟内容的格式：确保编号间有换行
        .replace(/(\d+\.\s*\*\*[^*]+\*\*[：:][^0-9\n]*?)(\d+\.)/g, '$1\n$2')

        // 确保减号列表项的正确缩进
        .replace(/^(\s*)-\s*(\*\*[^*]+\*\*)/gm, '$1- $2')

        // 清理格式：移除多余空行和空白字符
        .replace(/\n\s*\n\s*\n/g, '\n\n')
        .replace(/^\s+|\s+$/g, '')
        .replace(/\s+\n/g, '\n')
        .replace(/\n\s+/g, '\n')

        // 最终确保序号格式正确
        .replace(/(\d+\.)\s*\n\s*(\*\*[^*]+\*\*[：:])/g, '$1 $2')

        // 最终处理：确保标题前后有适当间距
        .replace(/(\n#{1,6}\s[^\n]+)\n([^\n#])/g, '$1\n\n$2')
        .replace(/([^\n])\n(#{1,6}\s)/g, '$1\n\n$2')

        // 特殊处理：确保标题和表格之间有空行（针对data1->data2的转换）
        .replace(/(#{1,6}\s[^\n]*)\n(\|[^|\n]*\|)/g, '$1\n\n$2')

        // 最终清理
        .trim()
      // console.log(formatted)
      return formatted
    }
  }
}
</script>

<style lang="less" scoped>
.markdown-viewer {
  text-align: left;

  ::v-deep {
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      margin-top: 12px;
      margin-bottom: 8px;
      font-weight: 600;
      line-height: 1.25;
    }

    h1 {
      font-size: 1.5em;
    }
    h2 {
      font-size: 1.3em;
    }
    h3 {
      font-size: 1.1em;
    }

    p {
      margin: 8px 0;
      line-height: 1.5;
    }

    pre {
      background-color: #f6f8fa;
      border-radius: 3px;
      padding: 12px;
      overflow: auto;
      font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
      font-size: 85%;
      line-height: 1.45;
      margin: 8px 0;
    }

    code {
      background-color: rgba(27, 31, 35, 0.05);
      border-radius: 3px;
      padding: 0.2em 0.4em;
      font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
      font-size: 85%;
    }

    pre code {
      background-color: transparent;
      padding: 0;
    }

    blockquote {
      margin: 8px 0;
      padding: 0 1em;
      color: #6a737d;
      border-left: 3px solid #dfe2e5;
    }

    ul,
    ol {
      padding-left: 2em;
      margin: 8px 0;
    }

    li {
      margin: 4px 0;
    }

    img {
      max-width: 100%;
      height: auto;
    }

    table {
      border-collapse: collapse;
      width: 100%;
      margin: 8px 0;
    }

    table th,
    table td {
      padding: 6px 13px;
      border: 1px solid #dfe2e5;
    }

    table tr:nth-child(2n) {
      background-color: #f6f8fa;
    }
  }
}
</style>
