<template>
  <!-- layout组件 im-layout -->
  <transition :name="transitionName">
    <div class="im-layout__container" :key="$route.path">
      <im-navbar v-if="!isTabPage"/>
      <router-view></router-view>
    </div>
  </transition>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'
import NavBar from './im-navbar'
import cons from '@/constants'

export default {
  name: 'ImLayout',
  components: {
    [NavBar.name]: NavBar
  },
  data () {
    return {
      socketio: null,
      transitionName: ''
    }
  },
  computed: {
    ...mapGetters(['username', 'avatarUrl', 'socket', 'isExpired', 'userId']),
    isTabPage () {
      const ua = navigator.userAgent.toLowerCase()
      if (ua.indexOf('micromessenger') !== -1) {
        return true
      } else {
        return this.$route.meta && this.$route.meta.tp
      }
    }
  },
  watch: {
    '$route' (to, from) {
      this.transitionName = ''
      if (to.meta.index > from.meta.index) {
        this.transitionName = 'fade-left'
      }
      if (to.meta.index < from.meta.index) {
        this.transitionName = 'fade-right'
      }
    }
  },
  mounted () {
    this.$bus.$on('accept-socket-message', this.msgHandler) // 接受socket消息
    this.$bus.$on('accept-local-message', this.msgHandler) // 接受本地消息广播，主要是本地图片
    if (!this.socket) {
      this.$store.commit('SET_TITLE', 'IT运维助手')
    }
    this.initData()
  },
  beforeDestroy () {
    this.$bus.$off('accept-socket-message', this.msgHandler)
    this.$bus.$off('accept-local-message', this.msgHandler)
  },
  methods: {
    ...mapMutations('user', ['SET_USER_NAME', 'SET_NICK_NAME', 'SET_USER_ID', 'SET_DEPT_NAME', 'SET_ROOM_ID']),

    // 初始化数据
    async initData () {
      await this.getUserInfo()
      await this.getRoomIdCode()
    },
    // 获取用户信息
    async getUserInfo () {
      const urlString = this.$route.fullPath
      const regex = /code=([^&]+)/
      const match = urlString.match(regex)
      const code = match ? match[1] : ''
      console.log('获取到的code码为:', code)
      if (!code) { // 如果没有code码，则直接使用临时信息
        this.SET_USER_NAME('admin')
        this.SET_NICK_NAME('admin')
        this.SET_USER_ID('admin')
        this.SET_DEPT_NAME('None')
        return Promise.resolve()
      }
      return this.$store.dispatch('user/getUserInfo', { code: code }).then(res => {
        console.log('获取用户信息成功', res)
      }).catch(err => {
        console.log('获取用户信息失败', err)
      })
    },

    // 获取房间号
    async getRoomIdCode () {
      const urlString = this.$route.fullPath
      const regex = /code=([^&]+)/
      const match = urlString.match(regex)
      const code = match ? match[1] : ''
      if (!code) {
        this.SET_ROOM_ID('')
        return Promise.resolve()
      }
      return this.$store.dispatch('user/getRoomId', { fromUser: this.userId }).then(res => {
        console.log('获取房间号成功', res)
      }).catch(err => {
        console.log('获取房间号失败', err)
      })
    },
    msgHandler (data) {
      // if (this.isExpired) {
      //   return
      // }
      let content = data.content
      if (data.type === cons.messageType.IMAGE) {
        content = '[图片]'
      }
      // 更新roomList中的lastMsg
      this.$store.dispatch('room/updateLastMsg', {
        // roomId: data.roomId,
        lastMsg: content,
        lastMsgTime: data.sendTime,
        name: data.from === this.username ? data.to : data.from
      })

      // 不在当前聊天页收到消息更新未读消息数
      // if (this.$route.path !== '/im-layout/chat-im/' + data.roomId) {
      //   this.$store.dispatch('room/updateUnReadMsg', {
      //     num: 1,
      //     roomId: data.roomId,
      //     type: cons.updateUnreadMessageNumberStyle.ADD
      //   })
      // }
    }
  }
}
</script>

<style lang="less" scoped>
  @import '@/assets/style/im-layout.less';
</style>
