<template>
    <!-- layout组件 im-layout -->
    <transition :name="transitionName">
      <div class="el-web-layout__container" :key="$route.path">
        <router-view></router-view>
      </div>
    </transition>
  </template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'ImLayout',
  components: {},
  data () {
    return {
      socketio: null,
      transitionName: ''
    }
  },
  computed: {
    ...mapGetters(['username', 'avatarUrl', 'socket', 'isExpired']),
    isTabPage () {
      return this.$route.meta && this.$route.meta.tp
    }
  },
  watch: {},
  mounted () {
    this.$bus.$on('accept-socket-message', this.msgHandler) // 接受socket消息
    this.$bus.$on('accept-local-message', this.msgHandler) // 接受本地消息广播，主要是本地图片
  },
  beforeDestroy () {
    this.$bus.$off('accept-socket-message', this.msgHandler)
    this.$bus.$off('accept-local-message', this.msgHandler)
  },
  methods: {
  }
}
</script>

  <style lang="less" scoped>
    @import '@/assets/style/im-web-layout.less';
  </style>
