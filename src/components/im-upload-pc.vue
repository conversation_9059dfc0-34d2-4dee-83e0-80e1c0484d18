<template>
    <el-upload
      :action="host + '/common/upload'"
      :headers="headers"
      :show-file-list="false"
      :on-success="handleSuccess"
      :before-upload="beforeUpload"
      style="display: inline-block"
    >
      <slot></slot>
    </el-upload>
  </template>

<script>
// import FetchRequest from '@/api/FetchRequest'
// import Auth from '@/api/Auth'
// import { ElMessage } from 'element-plus'
import MessageType from '../plugins/message-type.js'

export default {
  props: {
    fileTypes: {
      type: Array,
      required: true,
      default: () => []
    },
    isImg: {
      type: Boolean,
      required: true,
      default: true
    }
  },
  data () {
    return {
    //   host: FetchRequest.getHost(),
    //   headers: {
    //     'Access-Control-Allow-Origin': '*',
    //     Authorization: 'Bearer ' + Auth.getToken()
    //   }
    }
  },
  methods: {
    beforeUpload (file) {
      const suffix = file.name.substring(file.name.lastIndexOf('.') + 1)
      const suffixes = this.fileTypes
      const len = suffixes.filter((item) => item === suffix.toLowerCase()).length
      if (len === 0) {
        // ElMessage.error('不支持的文件类型,仅支持：' + suffixes.join(','))
      }
      return len > 0
    },
    handleSuccess (res) {
      this.$emit(
        'uploadSuccess',
        this.isImg ? { url: res.url } : { url: res.url, fileName: res.fileName },
        this.isImg ? MessageType.image : MessageType.file
      )
    }
  }
}
</script>

  <style scoped></style>
