<template>
  <!-- 导航组件 im-navbar -->
  <van-nav-bar style="background-color: #4871c0; color: #fff; align-items: center;" :title="title" left-text="">
    <template #left>
      <img class="logo" src="../assets/img/logo.png" alt="">
    </template>
  </van-nav-bar>
</template>

<script>

import { NavBar, Icon } from 'vant'
import { mapGetters } from 'vuex'
export default {
  name: 'ImNavbar',
  components: {
    [NavBar.name]: NavBar,
    [Icon.name]: Icon
  },
  computed: {
    ...mapGetters({
      activeTitle: 'title'
    }),
    title () {
      return this.$route.meta.title || this.activeTitle
    }
  },
  methods: {
    // onClickLeft () {
    //   this.$router.go(-1)
    // }
  }
}
</script>

<style lang="less" scoped>
  /deep/ .van-nav-bar__title {
    font-size: 16px;
    font-weight: 400;
    color: #fff;
    padding-left: .586667rem;  /* 22/37.5 */
  }
  /deep/ .van-icon-arrow-left {
    font-size: 20px;
  }

  /deep/ .van-nav-bar__content {
    justify-content: center;
    text-align: center;
  }

  /deep/ .van-nav-bar__left {
    left: 2.506667rem /* 94/37.5 */;
  }

  .logo {
    width: .64rem; /* 24/37.5 */
    height: .64rem; /* 24/37.5 */
  }
</style>
