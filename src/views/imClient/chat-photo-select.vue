<template>
  <div class="chat-photo-select__container">
    <section class="chat-photo-select__item" v-for="item in selectInfo" :key="item.id">
      <div>
        <div class="chat-photo-select__icon" @click="handleItemClick(item.id)">
          <van-icon :name="item.icon" size="30"/>
        </div>
        <input
          v-if="item.type === 'image'"
          class="chat-photo-select__file"
          type="file"
          accept="image/*"
          :capture="item.capture"
          :multiple="item.multiple"
          @change="fileImageHandler"
        />
        <input
          v-else-if="item.type === 'file'"
          class="chat-photo-select__file"
          type="file"
          accept="*"
          :multiple="item.multiple"
          @change="fileHandler"
        />
      </div>
      <p class="chat-photo-select__title">
        {{item.title}}
      </p>
    </section>

    <!-- Vant 评价弹出层 -->
    <van-dialog
      v-model="showRating"
      title="评价"
      show-cancel-button
      @confirm="submitRating"
      @cancel="cancelRating"
    >
      <div class="rating-content">
        <van-rate
          v-model="ratingScore"
          :count="5"
          color="#ffd21e"
          void-color="#eee"
        />
        <van-field
          v-model="ratingComment"
          type="textarea"
          placeholder="请输入评价内容（选填）"
          rows="3"
          autosize
          class="mt-2"
        />
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { Icon, Toast, Dialog, Field, Rate } from 'vant'
import { mapGetters } from 'vuex'

export default {
  name: 'ChatPhotoSelect',
  components: {
    [Icon.name]: Icon,
    [Dialog.Component.name]: Dialog.Component,
    [Field.name]: Field,
    [Rate.name]: Rate
  },
  data () {
    return {
      selectInfo: [{
        id: 1,
        icon: 'photo',
        type: 'image',
        capture: false,
        title: '相册',
        multiple: true
      },
      {
        id: 2,
        icon: 'description',
        type: 'file',
        capture: false,
        title: '文件',
        multiple: true
      },
      {
        id: 3,
        icon: 'records',
        type: 'evaluate',
        title: '评价',
        multiple: false
      },
      {
        id: 4,
        icon: 'clear',
        type: 'close',
        title: '关闭',
        multiple: false
      }
      ],
      isPlus: window.plus || false,
      showRating: false,
      ratingScore: 5,
      ratingComment: ''
    }
  },
  computed: {
    ...mapGetters(['username', 'sessionId', 'agentId', 'title', 'isExpired'])
  },
  methods: {
    handleItemClick (id) {
      switch (id) {
        case 1: // 相册
          if (this.isPlus) {
            console.log('选择相册')
          }
          break
        // case 2: // 拍照
        //   if (this.isPlus) {
        //     this.captureImage()
        //   }
        //   break
        case 3: // 评价
          this.showEvaluationDialog()
          break
        case 4: // 关闭
          this.handleCloseWebSocket()
          break
      }
    },
    showEvaluationDialog () {
      // 确保只有在移动端或支持plus的环境下才弹出原生评价框
      if (window.plus) {
        plus.nativeUI.prompt('请输入评价内容', (event) => {
          if (event.index === 1) {
            const content = event.text
            this.$bus.$emit('send-comment', content)
          }
        }, '', ['取消', '确定'])
      } else {
        // 重置评价数据
        this.ratingScore = 5
        this.ratingComment = ''
        this.showRating = true
      }
    },

    submitRating () {
      // 提交评价逻辑
      const ratingData = {
        score: this.ratingScore,
        comment: this.ratingComment.trim(),
        from: this.username,
        sessionId: this.sessionId
      }

      // 发送评价事件 暂时还不知道往哪里提交
      this.$bus.$emit('send-rating', ratingData)

      // 关闭弹窗
      this.showRating = false

      // 显示提交成功提示
      Toast('评价已提交')
    },
    cancelRating () {
      // 取消评价，重置数据
      this.ratingScore = 5
      this.ratingComment = ''
      this.showRating = false
    },
    fileImageHandler (e) {
      const files = e.target.files
      const typeList = ['png', 'jpeg', 'jpg', 'gif']
      if (files.length > 1) {
        return Toast('最多支持选择1张图片')
      }
      const formData = new FormData()
      for (const key in files) {
        if (files[key] instanceof File) {
          if (!typeList.includes(files[key].type.replace('image/', ''))) {
            return Toast('图片格式不支持')
          }
          if (files[key].size / 1024 / 1024 > 10) {
            return Toast('图片发送只支持10M以内')
          }
          formData.append('file', files[key])
        }
      }
      this.$bus.$emit('send-image', formData)
    },

    fileHandler (e) {
      const files = e.target.files
      if (files.length === 0) {
        return
      }
      if (!this.selectInfo.find(item => item.id === 2).multiple && files.length > 1) {
        return Toast('最多支持选择1个文件')
      }
      const formData = new FormData()
      for (const key in files) {
        if (files[key] instanceof File) {
          if (files[key].size / 1024 / 1024 > 50) {
            return Toast('文件发送只支持50M以内')
          }
          formData.append('file', files[key])
        }
      }
      this.$bus.$emit('send-file', formData)
    },
    // 退出socket连接
    handleCloseWebSocket () {
      if (this.sessionId !== '' && this.agentId !== '') {
        this.$store.commit('SET_TITLE', 'IT运维助手')
        this.$store.dispatch('socket/closeSession')
      }
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/style/chat-photo-select';

/deep/ .van-dialog {
  width: 80%!important;
}

/deep/ .van-dialog__confirm, .van-dialog__confirm:active {
 color: #4871c0;
}

.rating-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .van-rate {
    margin-bottom: 16px;
  }
}
</style>
