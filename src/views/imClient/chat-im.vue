<template>
  <!-- 聊天页面组件 chat-im -->
  <div class="chat-im__container" :style="{ top: hasTabPage ? '0' : '1.22667rem' }">
    <!-- 添加悬浮箭头组件 -->
    <div v-if="showScrollTopArrow && isRobotChat" class="scroll-top-arrow" @click="scrollToTop">
      <van-icon name="arrow-up" />
    </div>
    <im-scroll
      @touchstart.native="touchstartScroll"
      class="chat-im__scroller"
      ref="scroller"
      :has-more="hasMore"
      :load-status="loadStatus"
      :is-loading="isLoading"
      @loadmore="getMsgList"
    >
      <template #content>
        <ul class="chat-im__list-layout" ref="listLayout">
    <!-- <div class="chat-im-nav">
            <div class="chat-im-nav-box"  v-for="item in navIcons" :key="item.id">
              <img :src="item.icon" alt="" style="width: 24px; height: 24px;" @click="navClickHandler(item.url)" />
              <span style="font-size: 12px;">{{ item.title }}</span>
            </div>
          </div> -->
          <div class="question-box">
            <div class="question-title">猜你想问</div>
            <van-tabs v-model="activeTab" sticky :ellipsis="false">
              <van-tab v-for="(item, index) in transformedArray" :key="index" :title="item.category">
                <ul>
                  <li v-for="subItem in item.subQuestions"
                      :key="subItem.key"
                      @click="sendQuickQuestion(subItem.question)">
                    <span>{{ subItem.question }}</span>
                  </li>
                </ul>
                <!-- 只有当分类问题数量超过限制时才显示 -->
                <div v-if="item.totalQuestions > questionDisplayLimit"
                    class="question-more"
                    @click="toggleQuestionCategory(index)">
                  <span>{{ item.unfold ? '收起' : '展开更多' }}</span>
                  <van-icon :name="item.unfold ? 'arrow-up' : 'arrow-down'" />
                </div>
              </van-tab>
            </van-tabs>
          </div>
          <li class="chat-im__list-view" v-for="item in listAdapter" :key="item.id" :data-id="item.id">
            <chat-message-item
              :message="item"
              @imgload="imgLoadHandler"
            />
          </li>
        </ul>
      </template>
    </im-scroll>
    <chat-input v-if="socketSuccess"
      :input-height.sync="inputHeight"
      @send="sendSocketMessage"
      @focus="focusHandler"
      :is-show-select-area="isShowSelectArea"
      @show-select-area="showSelectArea"
      :input-icon.sync="inputIcon"
      :area-height="selectAreaHeight"
    />

    <chat-input v-else
      :input-height.sync="inputHeight"
      @send="sendHandler"
      @focus="focusHandler"
      :is-show-select-area="isShowSelectArea"
      @show-select-area="showSelectArea"
      :input-icon.sync="inputIcon"
      :area-height="selectAreaHeight"
    />
  </div>
</template>

<script>

import { mapGetters } from 'vuex'
import MessageItem from './chat-message-item'
import Scroller from '@/components/im-scroll'
import Input from './chat-input'
import messageHandler from '@/assets/js/message-handle'
// import md5 from 'md5'
import cons from '@/constants'
import { Tab, Tabs, Cell, CellGroup, Icon, Panel, Notify } from 'vant'

export default {
  name: 'ChatIm',
  components: {
    [Input.name]: Input,
    [MessageItem.name]: MessageItem,
    [Scroller.name]: Scroller,
    [Tab.name]: Tab,
    [Tabs.name]: Tabs,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Icon.name]: Icon,
    [Panel.name]: Panel,
    [Notify.name]: Notify
  },
  data () {
    return {
      listLayout: null,
      msgList: [],
      size: 1,
      page: 1,
      loadStatus: '',
      lastItemTimeStamp: 0, // 最后一条消息的时间戳
      hasMore: true, // 是否有下一页
      lastMsgSendTime: null, // 最后一条消息的发送时间
      beforePageHeight: 0, // 滚动前一页页面的高度
      isShowSelectArea: false, // select区域显示状态
      inputIcon: cons.input.icon.EMOJI, // 输入框旁的icon类型
      inputHeight: cons.input.HEIGHT, // 输入框高度
      selectAreaHeight: 0, // select区域高度
      scrollContainer: null, // 滚动容器元素
      activeTab: 0,
      questionDisplayLimit: 3,
      categoryUnfoldStates: {}, // 记录每个分类的展开状态
      dataArray: {},
      subQuestions: [],
      welcomeMessageSent: false, // 欢迎语是否已发送, 在欢迎语没有发出之前不可以发信息
      onHuman: false, // 是否正在转为人工客服的状态
      isWaitingForRobotResponse: false, // 是否正在等待机器人回复
      showScrollTopArrow: false, // 控制悬浮箭头的显示与隐藏
      isRobotChat: true // 判断当前是否是机器人对话
    }
  },
  computed: {
    ...mapGetters(['username', 'socket', 'nickName', 'deptName', 'userId', 'agentId', 'sessionId', 'activeRoomId', 'title', 'isIOS', 'roomId']),
    listAdapter () {
      return this.showTimeStampHandler(this.msgList)
    },
    isLoading () {
      return !!(this.loadStatus === 'loading' && this.hasMore === true && this.lastMsgSendTime)
    },
    hasTabPage () {
      const ua = navigator.userAgent.toLowerCase()
      if (ua.indexOf('micromessenger') !== -1) {
        return true
      } else {
        return false
      }
    },
    transformedArray () {
      return Object.entries(this.dataArray).map(([key, value], index) => {
        const subQuestions = value.map((question, questionIndex) => ({
          question: question,
          key: questionIndex
        }))

        // 获取当前分类的展开状态，默认为false
        const categoryUnfold = this.categoryUnfoldStates[index] || false

        return {
          category: key,
          // 根据展开状态控制显示的问题数量
          subQuestions: categoryUnfold
            ? subQuestions
            : subQuestions.slice(0, this.questionDisplayLimit),
          totalQuestions: subQuestions.length,
          unfold: categoryUnfold
        }
      })
    },
    socketSuccess () { // socket链接成功改为true，切换发送组件
      return this.$store.state.socket.socketSuccess
    }
  },
  watch: {
    inputHeight (newV, oldV) {
      if (newV === cons.input.HEIGHT) return
      this.$nextTick(() => {
        this.scrollContainer.scrollTop = this.listLayout.clientHeight
      })
    }
  },
  mounted () {
    // 添加滚动事件监听
    this.scrollContainer = this.$refs.scroller.$el
    this.scrollContainer.addEventListener('scroll', this.handleScroll)
    this.listLayout = this.$refs.listLayout
    this.scrollContainer = this.$refs.scroller.$el
    // this.$store.dispatch('room/updateUnReadMsg', { num: 0, roomId: this.activeRoomId }) // 消息已读
    this.$bus.$on('accept-socket-message', this.acceptSocketMessage)
    this.$bus.$on('accept-local-message', this.acceptLocalMessage)
    this.$bus.$on('send-image', this.sendImageHandler)
    this.$bus.$on('send-file', this.sendFileHandler)
    this.$bus.$on('backup', this.backupHandler)
    // 修改欢迎语定时器为可清除的实例
    this.getMsgList()
    this.welcomeTimer = setTimeout(() => {
      if (!this.welcomeMessageSent) {
        this.sendWelcomeMessage()
        this.welcomeMessageSent = true
      }
    }, 2000)
    this.getQuickQuestionList()
  },
  beforeDestroy () {
    this.$bus.$off('accept-socket-message', this.acceptSocketMessage)
    this.$bus.$off('accept-local-message', this.acceptLocalMessage)
    this.$bus.$off('send-image', this.sendImageHandler)
    this.$bus.$off('send-file', this.sendFileHandler)
    this.$bus.$off('backup', this.backupHandler)
    // 移除滚动事件监听
    this.scrollContainer.removeEventListener('scroll', this.handleScroll)
  },
  methods: {
    // 处理滚动事件
    handleScroll () {
      const questionBox = this.$el.querySelector('.question-box')
      if (questionBox) {
        const questionBoxBottom = questionBox.getBoundingClientRect().bottom
        this.showScrollTopArrow = questionBoxBottom < 0
      }
    },
    // 滚动到顶部
    scrollToTop () {
      this.scrollContainer.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    },

    // 新增欢迎语发送方法
    sendWelcomeMessage () {
      this.acceptLocalMessage({
        content: (this.nickName === 'admin' ? '您好,' : '您好,' + this.nickName) + '</br>我是您的IT运维小助手,7*24小时为您服务!',
        from: 'robot',
        id: `robot_${new Date().getTime()}_${Math.random().toString(36).substr(2, 9)}`,
        sendSocketId: '',
        sendTime: new Date().getTime(),
        to: this.userId,
        sessionId: this.sessionId,
        roomId: this.roomId,
        nickName: this.deptName + '-' + this.nickName,
        department: this.deptName,
        agentName: this.nickName,
        type: 3
      })
    },
    toggleQuestionCategory (index) {
      // 切换指定分类的展开状态
      this.$set(
        this.categoryUnfoldStates,
        index,
        !(this.categoryUnfoldStates[index] || false)
      )
    },
    /** 获取猜你想问列表 */
    getQuickQuestionList () {
      this.$api.guessList().then(res => {
        if (res.data.success) {
          this.dataArray = res.data.data
        }
      }).catch(err => {
        console.log(err)
      })
    },

    sendQuickQuestion (question) {
      // 发送用户问题
      if (this.agentId !== '' || this.onHuman === true) {
        return
      }
      // 如果正在等待机器人回复，不允许发送新消息
      if (this.isWaitingForRobotResponse) {
        Notify({ type: 'warning', message: '亲爱的用户，请您耐心等待前述问题的回答' })
        return
      }
      this.$store.commit('SET_TITLE', 'IT运维助手')
      this.sendHandler(question)
    },

    /** 跳转外部链接 */
    navClickHandler (url) {
      // TODO 跳转外部链接
      window.location.href = url
    },

    /** 切换监听 */
    tabHandler (idx) {
      this.activeTab = idx
      this.subQuestions = this.transformedArray[idx].subQuestions
    },

    backupHandler (cb) {
      if (this.isShowSelectArea) {
        this.hideSelectArea()
        cb(null, false)
      } else {
        cb(null, true)
      }
    },
    sendImageHandler (formData, isPlus) {
      this.hideSelectArea()
      if (isPlus) {
        this.sendImageMessage(formData)
      } else {
        this.$api.uploadImg(formData).then(res => {
          if (res.data.success) {
            this.sendImageMessage(res.data.data.url)
          }
        })
      }
    },
    sendFileHandler (formData, isPlus) {
      this.hideSelectArea()
      if (isPlus) {
        this.sendFileMessage(formData)
      } else {
        this.$api.uploadImg(formData).then(res => {
          if (res.data.success) {
            this.sendFileMessage(res.data.data.url)
          }
        })
      }
    },
    sendImageMessage (data) {
      if (this.socketSuccess) {
        this.sendSocketMessage(data, cons.messageType.IMAGE)
      } else {
        this.sendHandler(data, cons.messageType.IMAGE)
      }
    },
    sendFileMessage (data) {
      if (this.socketSuccess) {
        this.sendSocketMessage(data, cons.messageType.FILE)
      } else {
        this.sendHandler(data, cons.messageType.FILE)
      }
    },
    imgLoadHandler (sendTime) {
      const imgItemList = this.listAdapter.filter(val => val.type === 2 || val.type === 5)
      if (sendTime === imgItemList.reverse()[0].sendTime) {
        this.$nextTick(() => {
          this.scrollContainer.scrollTop = this.listLayout.clientHeight
        })
      }
    },
    focusHandler (focusType) {
      // 当焦点类型不是表情时执行以下操作
      if (focusType !== cons.input.focusType.EMOJI) {
        // 隐藏选择区域
        this.hideSelectArea()
        // Android设备上等待软键盘弹出后再滚动到底部
        setTimeout(() => {
          this.scrollContainer.scrollTop = this.listLayout.clientHeight
        }, 400)
        // 如果是iOS设备，根据焦点类型决定是否延迟显示键盘
        this.isIOS && this.isDelayShowKeyboard(focusType)
      }
    },
    /* 是否延时显示软键盘(解决IOS select区域显示时切换文本输入，输入框被软键盘遮挡) */
    isDelayShowKeyboard (focusType) {
      if (this.isShowSelectArea && focusType !== cons.input.focusType.KEYBOARD) {
        document.activeElement.blur()
        setTimeout(() => {
          this.$bus.$emit('delay-show-keyboard', cons.input.focusType.KEYBOARD)
        }, 300)
      }
    },

    // 删除机器人空消息消息并记录索引
    removeRobotLoadingMessage (currentSendTime) {
      // 查找消息在列表中的索引
      const index = this.msgList.findIndex(msg => msg.sendTime === currentSendTime)
      // 如果找到了，从列表中删除该消息
      if (index !== -1) {
        this.msgList.splice(index, 1)
      }
      // 返回索引，以便之后可以重新插入消息
      return index
    },

    // 新增的 socket 消息发送方法
    sendSocketMessage (content, type = cons.messageType.TEXT) {
      // 创建消息对象
      const messageData = {
        id: this.userId,
        from: this.userId,
        to: this.agentId || 'agent01',
        content: content,
        type: type,
        sendSocketId: this.socket.id,
        sessionId: this.sessionId,
        roomId: this.roomId,
        sendTime: new Date().getTime(),
        nickName: this.deptName + '-' + this.nickName,
        sendBy: 2
      }

      // 直接通过 socket 发送消息
      if (this.socket) {
        this.isRobotChat = false // 设置为人工对话
        this.onHuman = false // 装接成功后状态重置
        this.$store.dispatch('socket/sendMessage', messageData)
      } else {
        console.error('Socket 未连接')
      }

      // 自动滚动到底部
      this.$nextTick(() => {
        this.scrollContainer.scrollTop = this.listLayout.clientHeight
      })
    },

    // 机器人发送消息
    sendHandler (content, type = cons.messageType.TEXT) {
      // 设置为机器人对话
      this.isRobotChat = true
      if (!this.welcomeMessageSent) {
        return
      }
      if (this.onHuman) { // 如果正在转人工普通消息也不可以发
        Notify({ type: 'primary', message: '请稍等，转接中...' })
        return
      }

      // 如果正在等待机器人回复，不允许发送新消息
      if (this.isWaitingForRobotResponse) {
        Notify({ type: 'warning', message: '亲爱的用户，请您耐心等待前述问题的回答' })
        return
      }

      // 创建消息对象
      const newMessage = {
        id: `robot_${new Date().getTime()}_${Math.random().toString(36).substr(2, 9)}`,
        content: content,
        type: type,
        from: this.userId,
        roomId: this.roomId,
        sendTime: new Date().getTime(),
        nickName: this.deptName + '-' + this.nickName,
        to: 'robot',
        sessionId: this.sessionId,
        department: this.deptName,
        agentName: this.nickName
      }

      // 恢复发送表情功能
      const _data = _.cloneDeep(newMessage)
      messageHandler(_data)
      this.msgList.push(_data)

      // 设置等待机器人回复状态
      this.isWaitingForRobotResponse = true

      // 定义一个特殊的接收信息
      const robotMessage = {
        content: '',
        from: 'robot',
        id: `robot_${new Date().getTime()}_${Math.random().toString(36).substr(2, 9)}`,
        nickName: this.deptName + '-' + this.nickName,
        roomId: this.roomId,
        sendTime: '',
        department: this.deptName,
        agentName: this.nickName,
        type: 3
      }

      // 一秒没有返回先把这个消息接收到开始转圈圈
      const robotTimer = setTimeout(() => {
        robotMessage.sendTime = new Date().getTime()
        this.acceptLocalMessage(robotMessage)
      }, 1500)

      this.$api.sendToRobot({
        fromUser: this.username,
        question: content,
        roomId: this.roomId,
        department: this.deptName,
        agentName: this.nickName
      }).then(res => {
        // 请求成功后第一时间清楚定时器
        clearTimeout(robotTimer)
        // 删除加载消息，并记录索引
        const index = this.removeRobotLoadingMessage(robotMessage.sendTime)
        // 重置等待机器人回复状态
        this.isWaitingForRobotResponse = false
        if (res.data.code === 200) {
          const responseMessage = {
            content: res.data.result,
            from: 'robot',
            id: `robot_${new Date().getTime()}_${Math.random().toString(36).substr(2, 9)}`,
            roomId: this.roomId,
            sendTime: new Date().getTime(),
            nickName: this.deptName + '-' + this.nickName,
            sessionId: this.sessionId,
            department: this.deptName,
            agentName: this.nickName,
            type: 3
          }
          // 如果有索引，将消息插入到原来的位置
          if (index !== -1) {
            this.msgList.splice(index, 0, responseMessage)
          } else {
            this.msgList.push(responseMessage)
          }
          // 添加滚动到底部的逻辑
          this.$nextTick(() => {
            this.scrollContainer.scrollTop = this.listLayout.clientHeight
          })
        } else if (res.data.code === 500) {
          const errorMessage = {
            content: res.data.message,
            from: 'robot',
            id: `robot_${new Date().getTime()}_${Math.random().toString(36).substr(2, 9)}`,
            roomId: this.roomId,
            sendTime: new Date().getTime(),
            nickName: this.deptName + '-' + this.nickName,
            sessionId: this.sessionId,
            department: this.deptName,
            agentName: this.nickName,
            type: 3
          }
          if (!this.socket) {
            this.$store.dispatch('socket/createSocket')
            this.onHuman = true
            this.$store.commit('SET_TITLE', 'IT运维人工座席')
          }
          if (index !== -1) {
            this.msgList.splice(index, 0, errorMessage)
          } else {
            this.msgList.push(errorMessage)
          }
          // 添加滚动到底部的逻辑
          this.$nextTick(() => {
            this.scrollContainer.scrollTop = this.listLayout.clientHeight
          })
        } else {
          const defaultMessage = {
            content: '您好，it运维小助手走丢了，您可以输入"转人工"联系人工坐席进行处理',
            from: 'robot',
            id: `robot_${new Date().getTime()}`,
            roomId: this.roomId,
            sendTime: new Date().getTime(),
            nickName: this.deptName + '-' + this.nickName,
            sessionId: this.sessionId,
            department: this.deptName,
            agentName: this.nickName,
            type: 3
          }
          if (index !== -1) {
            this.msgList.splice(index, 0, defaultMessage)
          } else {
            this.msgList.push(defaultMessage)
          }
          // 添加滚动到底部的逻辑
          this.$nextTick(() => {
            this.scrollContainer.scrollTop = this.listLayout.clientHeight
          })
        }
      }).catch(rej => {
        // 请求失败时也清除定时器
        clearTimeout(robotTimer)
        // 删除加载消息，并记录索引
        const index = this.removeRobotLoadingMessage(robotMessage.sendTime)
        // 重置等待机器人回复状态
        this.isWaitingForRobotResponse = false
        const errorMessage = {
          content: '您好，it运维小助手走丢了，您可以输入"转人工"联系人工坐席进行处理',
          from: 'robot',
          id: `robot_${new Date().getTime()}`,
          roomId: this.roomId,
          sendTime: new Date().getTime(),
          nickName: this.deptName + '-' + this.nickName,
          sessionId: this.sessionId,
          department: this.deptName,
          agentName: this.nickName,
          type: 3
        }
        if (index !== -1) {
          this.msgList.splice(index, 0, errorMessage)
        } else {
          this.msgList.push(errorMessage)
        }
        // 添加滚动到底部的逻辑
        this.$nextTick(() => {
          this.scrollContainer.scrollTop = this.listLayout.clientHeight
        })
      })

      // 自动滚动到底部
      this.$nextTick(() => {
        this.scrollContainer.scrollTop = this.listLayout.clientHeight
      })
    },

    getMsgList () {
      if (!this.hasMore) {
        return
      }
      this.loadStatus = 'loading'
      this.lastMsgSendTime = (this.listAdapter[0] && this.listAdapter[0].sendTime) || null
      setTimeout(() => {
        this.$api.getHistoryChart({ // 接口获取聊天记录
          visitorId: this.userId
        }).then(res => {
          this.loadStatus = 'loaded'
          if (res.data.resultMsg === 'success') {
            const data = res.data.data
            if (data && data !== null) { // 第一次请求
              // 清除机器人欢迎语定时器
              clearTimeout(this.welcomeTimer)
              this.welcomeMessageSent = true
              // 自动创建socket连接
              if (!this.socket) {
                this.$store.dispatch('socket/createSocket')
                this.onHuman = true
                this.$store.commit('SET_TITLE', 'IT运维人工座席')
                // this.msgList = data
              }
              this.$nextTick(() => {
                this.scrollContainer.scrollTop = this.listLayout.clientHeight // 滚动到底
              })
            } else {
              this.msgList = data.concat(this.msgList)
              this.$nextTick(() => {
                this.scrollContainer.scrollTop = this.listLayout.clientHeight - this.beforePageHeight // 设置滚动到上次加载的位置
              })
            }
            if (data.length < this.size) {
              this.hasMore = false
            }
            this.beforePageHeight = this.listLayout.clientHeight
          }
        }).catch(rej => {
          this.loadStatus = 'loaded'
        })
      }, 500)
    },
    acceptLocalMessage (data) {
      this.hideSelectArea()
      this.acceptSocketMessage(data)
    },
    acceptSocketMessage (data) {
      console.log(data, '获取的聊天记录信息')
      const _data = _.cloneDeep(data)
      messageHandler(_data)
      this.msgList.push(_data)
      if (this.scrollContainer.scrollHeight - this.scrollContainer.clientHeight - this.scrollContainer.scrollTop < cons.MAX_SCROLL_INTO_VIEW_HEIGHT) {
        this.$nextTick(() => {
          this.scrollContainer.scrollTop = this.listLayout.clientHeight
        })
      }
    },
    showTimeStampHandler (data) { // 根据配置的显示时间间隔显示时间块
      if (data.length === 0) {
        return data
      }
      this.lastItemTimeStamp = data[0].sendTime
      for (let i = 0; i < data.length; i++) {
        if (i > 0 && data[i].sendTime < this.lastItemTimeStamp + cons.SHOW_CHAT_TIME_INTERVAL) {
          data[i].showTimeStamp = false
        } else {
          this.lastItemTimeStamp = data[i].sendTime
          data[i].showTimeStamp = true
        }
      }
      return data
    },
    escapeHTML (data) {
      data.forEach(val => messageHandler(val))
    },
    hideSelectArea () { // 隐藏emoji、图片选择区域
      if (this.isShowSelectArea) {
        this.selectAreaHeight = 0
        this.inputIcon = cons.input.icon.EMOJI
        setTimeout(() => {
          this.isShowSelectArea = false
        }, 300)
      }
    },

    touchstartScroll () {
      this.hideSelectArea()
      this.$bus.$emit('touch-scrollview', cons.input.focusType.TEXT)
      document.activeElement.blur()
    },

    showSelectArea () { // 显示emoji、图片选择区域
      this.isShowSelectArea = true
      this.selectAreaHeight = cons.input.SELECT_AREA_HEIGHT
      setTimeout(() => {
        this.scrollContainer.scrollTop = this.listLayout.scrollHeight
      }, 300)
    }
  }
}
</script>

<style lang="less" scoped>
  @import '@/assets/style/chat-im.less';
  .scroll-top-arrow {
    position: fixed;
    bottom: 100px;
    right: 20px;
    color: rgba(0, 0, 0, 0.1);
    width: 15px;
    height: 15px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }
  /deep/ .van-tabs__line {
    // background-color: linear-gradient(to right, navy, white);
    background-image: linear-gradient(to right, #5d82c7, #9bc5e7);
  }

  /deep/ .van-tab--active {
    font-weight: bold;
  }
</style>
