<template>
  <!-- 聊天内容展示组件 chat-message-item -->
  <section class="chat-message-item__container">
    <p class="chat-message-item__date-wrapper" v-if="message.showTimeStamp">
      <span class="chat-message-item__date-time">{{message.sendTime | format}}</span>
    </p>
    <div class="chat-message-item__all-content" :class="{self: message.from === username, other: message.from !== username}">
      <p class="chat-message-item__text-wrapper" v-if="message.type === 1 || message.type === '1' || message.type == 'text' || message.type === 'exit'">
        <span class="chat-message-item__text" v-html="message.content"></span>
        <span class="chat-message-item__angle"></span>
      </p>
      <p class="chat-message-item__text-wrapper" v-if="message.type === 3 || message.type === '3'">
        <span v-if="message.content && message.content.length > 0" class="chat-message-item__text" v-html="message.content"></span>
        <van-loading v-else type="spinner" size=".42rem"/>
        <span class="chat-message-item__angle"></span>
      </p>
      <div class="chat-message-item__image-wrapper" v-if="message.type === 2 || message.type === 'image' || message.type === '2'">
        <img class="chat-message-item__image" :src="message.content" @load="$emit('imgload', message.sendTime)" @click="onImageClick(message.content, 0, $event)"/>
      </div>
      <div class="chat-message-item__text-wrapper" v-if="message.type === 5 || message.type === 'file' || message.type === '5'">
        <a class="chat-message-item__text" style="color: #ffffff;" :href="message.content" target="_blank"><van-icon name="newspaper" color="#ffffff" />点击下载</a>
      </div>
      <div class="chat-message-item__avatar-wrapper">
        <img v-if="message.type === 3" class="chat-message-item__avatar" src="../../assets/img/avatar_robot.png" alt="">
        <img v-else-if="message.type === 'text' || message.type === 'image' || message.type === 'file' || message.type === 'exit'" class="chat-message-item__avatar" src="../../assets/img/avatar_sit.png" alt="">
        <img v-else-if="message.type === '5'" class="chat-message-item__avatar" :src="message.avatarUrl || defaultAvatar" alt="">
        <img v-else class="chat-message-item__avatar" :src="message.avatarUrl || defaultAvatar" alt="">
      </div>
    </div>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import { Loading, Icon, ImagePreview } from 'vant'
export default {
  name: 'ChatMessageItem',
  components: {
    [Loading.name]: Loading,
    [Icon.name]: Icon
  },
  props: {
    message: {
      type: Object
    }
  },
  computed: {
    ...mapGetters(['username', 'avatarUrl']),
    ...mapState(['defaultAvatar'])
  },
  methods: {
    // 图片点击处理
    onImageClick (images, index, event) {
      event.stopPropagation() // 阻止事件冒泡
      this.sceneImg(images, index)
    },
    // 图片预览
    sceneImg (images, index) {
      ImagePreview([images])
    }
  }
}
</script>

<style lang="less" scoped>
  @import '@/assets/style/chat-message-item.less';
</style>
