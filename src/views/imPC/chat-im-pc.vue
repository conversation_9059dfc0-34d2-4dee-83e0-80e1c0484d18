<template>
  <div class="redirect-container">
    <el-card class="redirect-card">
      <div class="text-center">
        <el-icon class="el-icon-mobile-phone" style="font-size: 50px; color: #67c23a;"></el-icon>
        <h2 style="margin-top: 20px;" @click="showSwitchMethod">
          <el-tooltip content="点击了解如何切换到手机模式" placement="top">
            <el-icon class="el-icon-question" style="font-size: 18px; color: #409EFF; cursor: pointer;"></el-icon>
          </el-tooltip>
          请切换到手机模式
        </h2>
        <p>当前页面功能仅支持移动端设备，请使用手机模式查看。</p>
        <el-button type="success" @click="goBack" style="margin-top: 15px;">返回聊天页面</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'RedirectToMobile',
  methods: {
    showSwitchMethod () {
      // 提示用户如何切换到手机模式
      this.$alert(
        '请按以下步骤切换到手机模式：<br>' +
        '1. 打开浏览器的开发者工具（F12 或右键选择“检查”）<br>' +
        '2. 点击设备图标（通常在左上角）切换到响应式模式<br>' +
        '3. 选择一个手机设备或自定义屏幕宽度',
        '如何切换到手机模式',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '知道了',
          type: 'info'
        }
      )
    },
    goBack () {
      // 跳转到指定页面
      this.$router.push('/im-layout/chat-im')
    }
  },
  mounted () {
    // 自动检测屏幕宽度，如果是移动端则跳转到首页
    if (window.innerWidth < 768) {
      this.$router.push('/im-layout/chat-im')
    }
  }
}
</script>

<style scoped>
.redirect-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f0f2f5;
}

.redirect-card {
  width: 100%;
  max-width: 400px;
  padding: 20px;
  text-align: center;
}

.text-center {
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
