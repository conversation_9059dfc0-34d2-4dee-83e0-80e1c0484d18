<template>
  <div class="pc-chat-container">
    <!-- 聊天头部 -->
    <chat-header
      :bot-info="currentBot"
      :file-mode="fileMode"
      :uploaded-files-count="uploadedFiles.length"
      @clear-messages="handleClearMessages"
    />

    <!-- 聊天内容区域 -->
    <chat-content
      :messages="messages"
      :is-typing="isTyping"
      @file-action="handleFileAction"
      @message-action="handleMessageAction"
    />

    <!-- 底部输入区域 -->
    <chat-input
      v-model="inputMessage"
      :is-typing="isTyping"
      :uploaded-files="uploadedFiles"
      :referenced-files="referencedFiles"
      :file-mode="fileMode"
      @send-message="handleSendMessage"
      @upload-file="handleUploadFile"
      @remove-reference="handleRemoveReference"
      @toggle-file-mode="handleToggleFileMode"
    />

    <!-- 文件预览/操作弹窗 -->
    <file-action-modal
      v-model="fileActionModalVisible"
      :file-info="selectedFileInfo"
      @reference-file="handleReferenceFile"
      @delete-file="handleDeleteFile"
    />

    <!-- Toast 提示组件 -->
    <toast-message ref="toast" />
  </div>
</template>

<script>
import ChatHeader from './chat-header.vue'
import ChatContent from './chat-content.vue'
import ChatInput from './chat-input.vue'
import FileActionModal from './file-action-modal.vue'
import ToastMessage from './toast-message.vue'
import { deleteAction, getAction, postAction } from './manage'
import { mapState } from 'vuex'

export default {
  name: 'PCChat',
  components: {
    [ChatHeader.name]: ChatHeader,
    [ChatContent.name]: ChatContent,
    [ChatInput.name]: ChatInput,
    [FileActionModal.name]: FileActionModal,
    [ToastMessage.name]: ToastMessage
  },
  data () {
    return {
      // 基础数据
      sessionId: '',
      inputMessage: '',
      isTyping: false,
      fileMode: true, // 文件模式开关，默认开启
      agent: '', // 用户代理
      stopOutput: false,
      timer: null,
      typewriterTimer: null, // 打字机效果定时器

      // 消息列表
      messages: [],

      // 机器人信息
      currentBot: {
        name: 'AI助手',
        avatar: require('@/assets/svg/ai-avatar.svg')
      },

      // 文件相关
      uploadedFiles: [], // 所有上传的文件
      referencedFiles: [], // 当前引用的文件
      selectedFileInfo: null,
      fileActionModalVisible: false,

      // 接口地址
      url: {
        // 非个人知识库问答
        noFileReferenceChat: '/deepseek/no-file-reference-chat',
        // 引用文件问答
        fileReferenceChat: '/deepseek/file-reference-chat',
        // 个人知识库问答
        lkbChat: '/deepseek/lkbChat',
        // 获取AI回复（轮询请求）
        aiInfoByRedis: '/deepseek/aiInfoByRedis',
        // 上传文件
        saveUploadFile: '/personalkbfiles/personalKbFiles/saveUploadFile',
        // 删除文件
        deleteFile: '/personalkbfiles/personalKbFiles/deleteFile',
        // 获取用户信息
        getUserInfo: '/ewxim/getUserId'
      }
    }
  },

  computed: {
    ...mapState({
      username: (state) => state.user.username
    })
  },

  async created () {
    await this.initAgent()
    this.generateSessionId()
    this.showWelcomeMessage()
  },

  beforeDestroy () {
    this.clearTimer()
    this.clearTypewriterTimer()
  },

  methods: {
    // 初始化用户代理
    async initAgent () {
      this.agent = this.username
      console.log('当前用户agent:', this.agent)

      if (!this.agent) {
        const urlParams = new URLSearchParams(window.location.search)
        const agentParam = urlParams.get('code')
        console.log('获取到URL参数agent:', agentParam)

        if (agentParam) {
          try {
            const userInfo = await this.fetchUserInfo(agentParam)
            if (userInfo) {
              this.agent = userInfo
              console.log('通过接口获取到的用户ID:', this.agent)
            } else {
              this.agent = agentParam
              console.log('接口调用失败，使用原始agent参数:', this.agent)
            }
          } catch (error) {
            console.error('获取用户信息失败:', error)
            this.agent = agentParam
            console.log('接口调用出错，使用原始agent参数:', this.agent)
          }
        }
      }
    },

    // 获取用户信息
    async fetchUserInfo (agent) {
      try {
        console.log('正在调用获取用户信息接口，agent参数:', agent)
        const urlWithParams = `${this.url.getUserInfo}?code=${agent}`
        const response = await postAction(urlWithParams, null)

        console.log('获取用户信息接口响应:', response)

        if (response && response.success && response.result) {
          console.log('成功获取用户ID:', response.result)
          return response.result
        } else {
          console.warn('获取用户信息失败，响应数据:', response)
          return null
        }
      } catch (error) {
        console.error('调用获取用户信息接口出错:', error)
        return null
      }
    },

    // 生成会话ID
    generateSessionId () {
      this.sessionId = Date.now()
      console.log('生成新的会话ID:', this.sessionId)
    },

    // 显示欢迎消息
    async showWelcomeMessage () {
      await this.$nextTick()

      const welcomeText = this.fileMode
        ? '您好！我是您的AI助手，已开启个人知识库模式。您可以上传文件，我会基于文件内容为您提供更准确的回答。'
        : '您好，我是您的智能客服AI助手，很高兴为您服务。我已经接入了最新的DeepSeek智能问答服务，在准确性、安全性和回答速度上，有了很大的提升，请问有什么我可以帮您的吗?'

      // 延迟一小段时间让页面稳定后再显示欢迎消息
      setTimeout(() => {
        const welcomeMessage = {
          id: this.generateMessageId(),
          type: 'ai',
          content: '正在输入...',
          timestamp: Date.now(),
          avatar: this.currentBot.avatar,
          basedOnFiles: []
        }

        this.messages.push(welcomeMessage)
        this.scrollToBottom()

        // 模拟打字机效果
        this.startTypewriter(welcomeMessage, welcomeText)
      }, 300)
    },

    // 生成消息ID
    generateMessageId () {
      return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    },

    // 开始打字机效果
    startTypewriter (message, targetText) {
      let currentIndex = 0
      message.content = ''

      this.typewriterTimer = setInterval(() => {
        if (currentIndex < targetText.length) {
          message.content += targetText[currentIndex]
          currentIndex++
          this.scrollToBottom()
        } else {
          this.clearTypewriterTimer()
        }
      }, 50)
    },

    // 清除打字机定时器
    clearTypewriterTimer () {
      if (this.typewriterTimer) {
        clearInterval(this.typewriterTimer)
        this.typewriterTimer = null
      }
    },

    // 滚动到底部
    scrollToBottom () {
      this.$nextTick(() => {
        const container = this.$el.querySelector('.chat-content')
        if (container) {
          container.scrollTop = container.scrollHeight
        }
      })
    },

    // 处理发送消息
    async handleSendMessage (messageData) {
      console.log('发送消息:', messageData)

      // 构建用户消息
      const userMessage = {
        id: this.generateMessageId(),
        type: 'user',
        content: messageData.text,
        timestamp: Date.now(),
        referencedFiles: messageData.referencedFiles || []
      }

      this.messages.push(userMessage)
      this.scrollToBottom()

      // 构建AI回复消息
      const aiMessage = {
        id: this.generateMessageId(),
        type: 'ai',
        content: '正在输入...',
        timestamp: Date.now(),
        avatar: this.currentBot.avatar,
        basedOnFiles: []
      }

      this.messages.push(aiMessage)
      this.scrollToBottom()

      this.isTyping = true

      try {
        // 准备请求数据
        const requestData = {
          sessionId: this.sessionId,
          question: messageData.text,
          agent: this.agent
        }

        let apiUrl = ''
        let requestFileIds = []

        // 根据文件模式和引用文件选择API
        if (this.fileMode) {
          if (messageData.referencedFiles && messageData.referencedFiles.length > 0) {
            // 引用文件问答
            apiUrl = this.url.fileReferenceChat
            requestFileIds = messageData.referencedFiles.map(file => file.id)
            requestData.fileIds = requestFileIds
          } else {
            // 个人知识库问答
            apiUrl = this.url.lkbChat
          }
        } else {
          // 非个人知识库问答
          apiUrl = this.url.noFileReferenceChat
        }

        console.log('发送AI问答请求:', {
          url: apiUrl,
          data: requestData
        })

        // 发送请求
        const response = await postAction(apiUrl, requestData)
        console.log('AI问答响应:', response)

        if (response && response.data && response.data.success) {
          // 开始轮询获取AI回复
          await this.pollAIResponse(aiMessage, requestFileIds)
        } else {
          throw new Error(response?.data?.message || '请求失败')
        }
      } catch (error) {
        console.error('发送消息失败:', error)
        aiMessage.content = '抱歉，服务暂时不可用，请稍后重试。'
        this.$refs.toast?.show('发送失败，请稍后重试', 'error')
      } finally {
        this.isTyping = false
      }
    },

    // 轮询获取AI回复
    async pollAIResponse (aiMessage, referencedFileIds = []) {
      const maxAttempts = 30 // 最大轮询次数
      let attempts = 0

      const poll = async () => {
        if (this.stopOutput || attempts >= maxAttempts) {
          if (attempts >= maxAttempts) {
            aiMessage.content = '抱歉，响应超时，请稍后重试。'
          }
          this.isTyping = false
          return
        }

        attempts++

        try {
          const response = await getAction(this.url.aiInfoByRedis, {
            sessionId: this.sessionId,
            agent: this.agent
          })

          console.log(`轮询第${attempts}次响应:`, response)

          if (response && response.data && response.data.success && response.data.result) {
            const result = response.data.result

            if (result.content) {
              // 获取到内容，停止轮询
              aiMessage.content = result.content

              // 处理引用文件信息
              if (result.basedOnFiles && result.basedOnFiles.length > 0) {
                aiMessage.basedOnFiles = result.basedOnFiles
              } else if (referencedFileIds.length > 0) {
                // 如果没有返回basedOnFiles但有引用文件，使用引用的文件
                aiMessage.basedOnFiles = this.referencedFiles.filter(file =>
                  referencedFileIds.includes(file.id)
                )
              }

              this.isTyping = false
              this.scrollToBottom()
              return
            }
          }

          // 继续轮询
          this.timer = setTimeout(poll, 2000)
        } catch (error) {
          console.error('轮询AI回复失败:', error)
          aiMessage.content = '抱歉，获取回复失败，请稍后重试。'
          this.isTyping = false
        }
      }

      poll()
    },

    // 处理文件上传
    async handleUploadFile (file) {
      console.log('上传文件:', file)

      const fileMessage = {
        id: this.generateMessageId(),
        type: 'file',
        timestamp: Date.now(),
        fileInfo: {
          id: Date.now(),
          name: file.name,
          size: file.size,
          type: file.type,
          status: 'uploading'
        }
      }

      this.messages.push(fileMessage)
      this.scrollToBottom()

      try {
        const formData = new FormData()
        formData.append('file', file)
        formData.append('agent', this.agent)

        const response = await postAction(this.url.saveUploadFile, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })

        console.log('文件上传响应:', response)

        if (response && response.data && response.data.success) {
          // 上传成功
          fileMessage.fileInfo.status = 'uploaded'
          fileMessage.fileInfo.id = response.data.result.id || fileMessage.fileInfo.id

          // 添加到上传文件列表
          this.uploadedFiles.push(fileMessage.fileInfo)

          this.$refs.toast?.show('文件上传成功', 'success')
        } else {
          throw new Error(response?.data?.message || '上传失败')
        }
      } catch (error) {
        console.error('文件上传失败:', error)
        fileMessage.fileInfo.status = 'failed'
        this.$refs.toast?.show('文件上传失败', 'error')
      }
    },

    // 处理文件操作
    handleFileAction (fileInfo) {
      console.log('文件操作:', fileInfo)
      this.selectedFileInfo = fileInfo
      this.fileActionModalVisible = true
    },

    // 处理引用文件
    handleReferenceFile (fileInfo) {
      console.log('引用文件:', fileInfo)

      // 检查是否已经引用过
      const existingIndex = this.referencedFiles.findIndex(f => f.id === fileInfo.id)
      if (existingIndex === -1) {
        this.referencedFiles.push(fileInfo)
        this.$refs.toast?.show(`已引用文件：${fileInfo.name}`, 'success')
      } else {
        this.$refs.toast?.show('该文件已经被引用', 'warning')
      }

      this.fileActionModalVisible = false
    },

    // 处理删除文件
    async handleDeleteFile (fileInfo) {
      console.log('删除文件:', fileInfo)

      try {
        const response = await deleteAction(this.url.deleteFile, {
          id: fileInfo.id,
          agent: this.agent
        })

        console.log('删除文件响应:', response)

        if (response && response.data && response.data.success) {
          // 从上传文件列表中移除
          const uploadedIndex = this.uploadedFiles.findIndex(f => f.id === fileInfo.id)
          if (uploadedIndex !== -1) {
            this.uploadedFiles.splice(uploadedIndex, 1)
          }

          // 从引用文件列表中移除
          const referencedIndex = this.referencedFiles.findIndex(f => f.id === fileInfo.id)
          if (referencedIndex !== -1) {
            this.referencedFiles.splice(referencedIndex, 1)
          }

          // 更新消息中的文件状态
          this.messages.forEach(message => {
            if (message.fileInfo && message.fileInfo.id === fileInfo.id) {
              message.fileInfo.status = 'deleted'
            }
          })

          this.$refs.toast?.show('文件删除成功', 'success')
        } else {
          throw new Error(response?.data?.message || '删除失败')
        }
      } catch (error) {
        console.error('删除文件失败:', error)
        this.$refs.toast?.show('文件删除失败', 'error')
      }

      this.fileActionModalVisible = false
    },

    // 处理移除引用
    handleRemoveReference (fileId) {
      console.log('移除引用:', fileId)
      const index = this.referencedFiles.findIndex(f => f.id === fileId)
      if (index !== -1) {
        this.referencedFiles.splice(index, 1)
      }
    },

    // 处理切换文件模式
    handleToggleFileMode () {
      this.fileMode = !this.fileMode
      console.log('切换文件模式:', this.fileMode ? '开启' : '关闭')

      const modeText = this.fileMode ? '个人知识库模式已开启' : '个人知识库模式已关闭'
      this.$refs.toast?.show(modeText, 'info')
    },

    // 处理清空消息
    handleClearMessages () {
      console.log('清空聊天记录')
      this.messages = []
      this.uploadedFiles = []
      this.referencedFiles = []
      this.generateSessionId()
      this.showWelcomeMessage()
      this.$refs.toast?.show('聊天记录已清空', 'success')
    },

    // 处理消息操作
    handleMessageAction (action, message) {
      console.log('消息操作:', action, message)
      // 可以根据需要添加消息操作逻辑
    },

    // 清除定时器
    clearTimer () {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
    }
  }
}
</script>

<style scoped>
.pc-chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f7fa;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  max-width: 1200px;
  margin: 0 auto;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* PC端适配样式 */
@media (min-width: 768px) {
  .pc-chat-container {
    border-radius: 12px;
    margin: 20px auto;
    height: calc(100vh - 40px);
    overflow: hidden;
  }
}

/* 响应式设计 */
@media (max-width: 767px) {
  .pc-chat-container {
    height: 100vh;
    margin: 0;
    border-radius: 0;
  }
}
</style>
