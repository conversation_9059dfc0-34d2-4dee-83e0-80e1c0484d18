<template>
  <div class="h5-chat-container">
    <!-- 聊天头部 -->
    <chat-header
      :bot-info="currentBot"
      :file-mode="fileMode"
      :uploaded-files-count="uploadedFiles.length"
      @clear-messages="handleClearMessages"
    />

    <!-- 聊天内容区域 -->
    <chat-content
      :messages="messages"
      :is-typing="isTyping"
      @file-action="handleFileAction"
      @message-action="handleMessageAction"
    />

    <!-- 底部输入区域 -->
    <chat-input
      v-model="inputMessage"
      :is-typing="isTyping"
      :uploaded-files="uploadedFiles"
      :referenced-files="referencedFiles"
      :file-mode="fileMode"
      @send-message="handleSendMessage"
      @upload-file="handleUploadFile"
      @remove-reference="handleRemoveReference"
      @toggle-file-mode="handleToggleFileMode"
    />

    <!-- 文件预览/操作弹窗 -->
    <file-action-modal
      v-model="fileActionModalVisible"
      :file-info="selectedFileInfo"
      @reference-file="handleReferenceFile"
      @delete-file="handleDeleteFile"
    />

    <!-- Toast 提示组件 -->
    <toast-message ref="toast" />
  </div>
</template>

<script>
import ChatHeader from './chat-header.vue'
import ChatContent from './chat-content.vue'
import ChatInput from './chat-input.vue'
import FileActionModal from './file-action-modal.vue'
import ToastMessage from './toast-message.vue'
import { deleteAction, getAction, postAction } from './manage'
import { mapState } from 'vuex'

export default {
  name: 'H5Chat',
  components: {
    [ChatHeader.name]: ChatHeader,
    [ChatContent.name]: ChatContent,
    [ChatInput.name]: ChatInput,
    [FileActionModal.name]: FileActionModal,
    [ToastMessage.name]: ToastMessage
  },
  data () {
    return {
      // 基础数据
      sessionId: '',
      inputMessage: '',
      isTyping: false,
      fileMode: true, // 文件模式开关，默认开启
      agent: '', // 用户代理
      stopOutput: false,
      timer: null,
      typewriterTimer: null, // 打字机效果定时器

      // 消息列表
      messages: [],

      // 机器人信息
      currentBot: {
        name: 'AI助手',
        avatar: require('@/assets/svg/ai-avatar.svg')
      },

      // 文件相关
      uploadedFiles: [], // 所有上传的文件
      referencedFiles: [], // 当前引用的文件
      selectedFileInfo: null,
      fileActionModalVisible: false,

      // 接口地址
      url: {
        // 非个人知识库问答
        noFileReferenceChat: '/deepseek/no-file-reference-chat',
        // 引用文件问答
        fileReferenceChat: '/deepseek/file-reference-chat',
        // 个人知识库问答
        lkbChat: '/deepseek/lkbChat',
        // 获取AI回复（轮询请求）
        aiInfoByRedis: '/deepseek/aiInfoByRedis',
        // 上传文件
        saveUploadFile: '/personalkbfiles/personalKbFiles/saveUploadFile',
        // 删除文件
        deleteFile: '/personalkbfiles/personalKbFiles/deleteFile',
        // 获取用户信息
        getUserInfo: '/ewxim/getUserId'
      }
    }
  },

  computed: {
    ...mapState({
      username: (state) => state.user.username
    })
  },

  async created () {
    await this.initAgent()
    this.generateSessionId()
    this.showWelcomeMessage()
  },

  beforeDestroy () {
    this.clearTimer()
    this.clearTypewriterTimer()
  },

  methods: {
    // 初始化用户代理
    async initAgent () {
      this.agent = this.username
      console.log('当前用户agent:', this.agent)

      if (!this.agent) {
        const urlParams = new URLSearchParams(window.location.search)
        const agentParam = urlParams.get('code')
        console.log('获取到URL参数agent:', agentParam)

        if (agentParam) {
          try {
            const userInfo = await this.fetchUserInfo(agentParam)
            if (userInfo) {
              this.agent = userInfo
              console.log('通过接口获取到的用户ID:', this.agent)
            } else {
              this.agent = agentParam
              console.log('接口调用失败，使用原始agent参数:', this.agent)
            }
          } catch (error) {
            console.error('获取用户信息失败:', error)
            this.agent = agentParam
            console.log('接口调用出错，使用原始agent参数:', this.agent)
          }
        }
      }
    },

    // 获取用户信息
    async fetchUserInfo (agent) {
      try {
        console.log('正在调用获取用户信息接口，agent参数:', agent)
        const urlWithParams = `${this.url.getUserInfo}?code=${agent}`
        const response = await postAction(urlWithParams, null)

        console.log('获取用户信息接口响应:', response)

        if (response && response.success && response.result) {
          console.log('成功获取用户ID:', response.result)
          return response.result
        } else {
          console.warn('获取用户信息失败，响应数据:', response)
          return null
        }
      } catch (error) {
        console.error('调用获取用户信息接口出错:', error)
        return null
      }
    },

    // 生成会话ID
    generateSessionId () {
      this.sessionId = Date.now()
      console.log('生成新的会话ID:', this.sessionId)
    },

    // 显示欢迎消息
    async showWelcomeMessage () {
      await this.$nextTick()

      const welcomeText = this.fileMode
        ? '您好！我是您的AI助手，已开启个人知识库模式。您可以上传文件，我会基于文件内容为您提供更准确的回答。'
        : '您好，我是您的智能客服AI助手，很高兴为您服务。我已经接入了最新的DeepSeek智能问答服务，在准确性、安全性和回答速度上，有了很大的提升，请问有什么我可以帮您的吗?'

      // 延迟一小段时间让页面稳定后再显示欢迎消息
      setTimeout(() => {
        const welcomeMessage = {
          id: this.generateMessageId(),
          type: 'ai',
          content: '正在输入...',
          timestamp: Date.now(),
          avatar: this.currentBot.avatar,
          isTypingCursor: true // 标记为打字状态
        }

        this.messages.push(welcomeMessage)

        // 使用打字机效果显示欢迎消息
        setTimeout(() => {
          this.startTypewriterEffect(welcomeMessage, welcomeText)
        }, 100)
      }, 300)
    },

    // 处理发送消息
    async handleSendMessage (content) {
      // 参数验证：确保有内容或有引用文件
      if (!content.trim() && this.referencedFiles.length === 0) {
        if (this.$refs.toast) {
          this.$refs.toast.show('请输入问题内容', 'warning')
        }
        return
      }

      // 非文件模式下，必须有文本内容
      if (!this.fileMode && !content.trim()) {
        if (this.$refs.toast) {
          this.$refs.toast.show('请输入问题内容', 'warning')
        }
        return
      }

      // 未选中个人知识库时，不能引用文件
      if (!this.fileMode && this.referencedFiles.length > 0) {
        if (this.$refs.toast) {
          this.$refs.toast.show(
            '请先开启个人知识库模式才能引用文件',
            'warning'
          )
        }
        return
      }

      // 添加用户消息
      const userMessage = {
        id: this.generateMessageId(),
        type: 'user',
        content: content,
        timestamp: Date.now(),
        referencedFiles: [...this.referencedFiles]
      }
      this.messages.push(userMessage)

      // 清空输入
      this.inputMessage = ''
      this.scrollToBottom()

      // 开始AI回复
      this.isTyping = true
      this.stopOutput = false

      try {
        // 生成新的会话ID
        this.generateSessionId()

        // 根据不同情况选择不同的接口
        if (!this.fileMode) {
          // 1. 未选中【个人知识库】，调用 no-file-reference-chat
          this.handleNormalChat(content)
        } else if (this.referencedFiles.length > 0) {
          // 3. 选中【个人知识库】，并引用某个文件，调用 file-reference-chat
          this.handleFileReferenceChat(content)
        } else {
          // 2. 选中【个人知识库】，调用 lkbChat
          this.handleKnowledgeBaseChat(content)
        }

        // 开始轮询获取AI回复
        this.startPolling()
      } catch (error) {
        console.error('发送消息失败:', error)
        this.handleErrorResponse('抱歉，我遇到了一些问题，请稍后重试。')
      }
    },

    // 处理普通聊天（无文件引用）
    async handleNormalChat (content) {
      // 参数验证
      if (!content || !content.trim()) {
        this.handleErrorResponse('问题内容不能为空')
        return
      }

      const requestData = {
        question: content.trim(),
        key: 'noFileReferenceChat:' + this.sessionId
      }

      console.log('无文件引用聊天请求参数:', requestData)

      const response = await postAction(
        this.url.noFileReferenceChat,
        requestData
      )
      console.log('无文件引用聊天API响应:', response)

      // 错误处理
      if (response && response.code === 500) {
        const errorMessage = response.message || '请求失败，请稍后重试'
        this.handleErrorResponse(errorMessage)
        return
      }

      if (response === 'error' || (response && response.message === 'error')) {
        throw new Error('API返回错误')
      }

      if (!response || !response.success) {
        const errorMessage =
          (response && response.message) || '请求失败，请稍后重试'
        this.handleErrorResponse(errorMessage)
      }
    },

    // 处理引用文件聊天
    async handleFileReferenceChat (content) {
      // 参数验证
      if (!content || !content.trim()) {
        this.handleErrorResponse('问题内容不能为空')
        return
      }

      if (!this.agent) {
        this.handleErrorResponse('用户信息获取失败，请刷新页面重试')
        return
      }

      if (this.referencedFiles.length === 0) {
        this.handleErrorResponse('请先引用文件后再提问')
        return
      }

      // 获取所有引用文件的文件名，多个文件用分号分隔
      const fileNames = this.referencedFiles
        .map((file) => file.name)
        .filter((name) => name)
      if (fileNames.length === 0) {
        this.handleErrorResponse('引用文件信息不完整')
        return
      }

      const fileName = fileNames.join(';')
      console.log(
        `用户引用了${this.referencedFiles.length}个文件：${fileName}`
      )

      const requestData = {
        question: content.trim(),
        userId: this.agent,
        key: 'fileReferenceChat:' + this.sessionId,
        type: 'AI_MULTIPLE_ROUNDS_CHAT',
        fileName: fileName
      }

      console.log('引用文件聊天请求参数:', requestData)

      const response = await postAction(
        this.url.fileReferenceChat,
        requestData
      )
      console.log('引用文件聊天API响应:', response)

      // 错误处理
      if (response && response.code === 500) {
        const errorMessage = response.message || '文件处理失败，请稍后重试'
        this.handleErrorResponse(errorMessage)
        return
      }

      if (response === 'error' || (response && response.message === 'error')) {
        throw new Error('API返回错误')
      }

      if (!response || !response.success) {
        const errorMessage =
          (response && response.message) || '请求失败，请稍后重试'
        this.handleErrorResponse(errorMessage)
      }
    },

    // 处理知识库聊天
    async handleKnowledgeBaseChat (content) {
      const requestData = {
        longSentence: content,
        agent: this.agent,
        key: this.sessionId,
        type: 'AI_MULTIPLE_ROUNDS_CHAT'
      }

      const response = await postAction(this.url.lkbChat, requestData)
      console.log('知识库聊天API响应:', response)

      if (response === 'error' || (response && response.message === 'error')) {
        throw new Error('API返回错误')
      }

      if (
        response === 0 ||
        response === '0' ||
        (response && (response.message === 0 || response.message === '0'))
      ) {
        // 重新生成会话ID并重试
        this.generateSessionId()
        const retryRequestData = {
          ...requestData,
          key: this.sessionId
        }
        await postAction(this.url.lkbChat, retryRequestData)
      }
    },

    // 开始轮询获取AI回复
    startPolling () {
      this.clearTimer()

      let aiMessage = null // AI消息引用
      let lastContent = '' // 上次内容

      this.timer = setInterval(async () => {
        if (this.stopOutput) {
          this.clearTimer()
          this.isTyping = false
          return
        }

        try {
          let requestStr
          if (!this.fileMode) {
            // 1. 未选中【个人知识库】，调用 no-file-reference-chat
            requestStr = 'noFileReferenceChat:' + this.sessionId
          } else if (this.referencedFiles.length > 0) {
            // 3. 选中【个人知识库】，并引用某个文件，调用 file-reference-chat
            requestStr = 'fileReferenceChat:' + this.sessionId
          } else {
            // 2. 选中【个人知识库】，调用 lkbChat
            requestStr = 'lkbchat:' + this.sessionId
          }

          const response = await getAction(this.url.aiInfoByRedis, {
            key: requestStr
          })

          if (response && response.result && response.result.answerByGPT) {
            const aiResponseContent = response.result.answerByGPT
            if (aiResponseContent && aiResponseContent.trim() !== '') {
              // 只有在有内容时才创建AI消息
              if (!aiMessage) {
                aiMessage = {
                  id: this.generateMessageId(),
                  type: 'ai',
                  content: '',
                  timestamp: Date.now(),
                  avatar: this.currentBot.avatar
                }
                this.messages.push(aiMessage)
              }

              if (aiResponseContent.indexOf('endTrue') !== -1) {
                this.stopOutput = true
                const finalContent = aiResponseContent.replace('endTrue', '')
                // 如果有新内容，使用打字机效果显示
                if (finalContent !== lastContent) {
                  this.startTypewriterEffect(aiMessage, finalContent)
                }
              } else {
                // 如果有新内容，使用打字机效果显示
                if (aiResponseContent !== lastContent) {
                  this.startTypewriterEffect(aiMessage, aiResponseContent)
                  lastContent = aiResponseContent
                }
              }
            }
          }
        } catch (error) {
          console.error('轮询获取AI回复失败:', error)
        }
      }, 200)
    },

    // 处理错误响应
    handleErrorResponse (errorMessage) {
      const aiMessage = {
        id: this.generateMessageId(),
        type: 'ai',
        content: '',
        timestamp: Date.now(),
        avatar: this.currentBot.avatar,
        isError: true
      }
      this.messages.push(aiMessage)

      // 使用打字机效果显示错误消息
      this.startTypewriterEffect(aiMessage, errorMessage)

      this.isTyping = false
      this.stopOutput = true
    },

    // 清除定时器
    clearTimer () {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },

    // 清除打字机定时器
    clearTypewriterTimer () {
      if (this.typewriterTimer) {
        clearTimeout(this.typewriterTimer)
        this.typewriterTimer = null
      }
    },

    // 打字机效果显示文本
    startTypewriterEffect (aiMessage, targetContent) {
      // 清除之前的打字机效果
      this.clearTypewriterTimer()

      const currentContent = aiMessage.content || ''

      // 如果目标内容和当前内容相同，不需要打字机效果
      if (targetContent === currentContent) {
        return
      }

      // 如果消息标记为打字状态，从头开始打字机效果
      if (aiMessage.isTypingCursor) {
        aiMessage.isTypingCursor = false // 移除打字状态标记
        this.typewriterDisplay(aiMessage, '', targetContent)
        return
      }

      // 只显示新增的部分
      if (targetContent.startsWith(currentContent)) {
        const newText = targetContent.slice(currentContent.length)
        this.typewriterDisplay(aiMessage, currentContent, newText)
      } else {
        // 如果不是追加模式，重新开始打字机效果
        this.typewriterDisplay(aiMessage, '', targetContent)
      }
    },

    // 执行打字机显示动画
    typewriterDisplay (aiMessage, baseContent, newText, index = 0) {
      if (index <= newText.length) {
        aiMessage.content = baseContent + newText.slice(0, index)
        this.scrollToBottom()

        if (index < newText.length) {
          const delay = 30
          this.typewriterTimer = setTimeout(() => {
            this.typewriterDisplay(aiMessage, baseContent, newText, index + 1)
          }, delay)
        }
      }
    },

    // 滚动到底部
    scrollToBottom () {
      this.$nextTick(() => {
        const chatContent = this.$el.querySelector('.h5-chat-content')
        if (chatContent) {
          chatContent.scrollTop = chatContent.scrollHeight
        }
      })
    },

    // 处理文件上传（支持单个或多个文件）
    async handleUploadFile (files) {
      // 将单个文件转换为数组
      const fileArray = Array.isArray(files) ? files : [files]

      if (!fileArray.length) return

      // 检查文件数量限制
      if (this.uploadedFiles.length + fileArray.length > 10) {
        this.$refs.toast.show('文件数量超出限制，最多支持10个文件', 'error')
        return
      }

      // 过滤有效文件并立即显示在消息列表中
      const validFiles = fileArray.filter((file) => {
        if (file.size > 10 * 1024 * 1024) {
          this.$refs.toast.show(
            `文件 "${file.name}" 大小超过10MB，已跳过`,
            'error'
          )
          return false
        }
        return true
      })

      if (validFiles.length === 0) {
        this.$refs.toast.show('没有有效的文件可以上传', 'warning')
        return
      }

      // 为每个文件立即创建消息并显示上传中状态
      const uploadingMessages = validFiles.map((file) => {
        const uploadingMessage = {
          id: this.generateMessageId(),
          type: 'user',
          content: `📎 ${file.name}`,
          timestamp: Date.now(),
          fileInfo: {
            name: file.name,
            size: file.size,
            type: file.type,
            status: 'uploading' // 上传中状态
          }
        }
        this.messages.push(uploadingMessage)
        this.scrollToBottom()
        return { message: uploadingMessage, file }
      })

      // 并行上传所有文件
      const uploadPromises = uploadingMessages.map(({ message, file }) =>
        this.uploadSingleFileWithMessage(file, message)
      )

      try {
        await Promise.allSettled(uploadPromises)

        // 同步更新消息中的文件ID，确保与服务器一致
        this.syncMessageFileIds(uploadingMessages)
      } catch (error) {
        console.error('文件上传出错:', error)
        this.$refs.toast.show('文件上传失败，请重试', 'error')
      }
    },

    // 上传单个文件
    async uploadSingleFile (file) {
      try {
        const formData = new FormData()
        formData.append('files', file)
        formData.append('userId', this.agent || 'default')

        const response = await postAction(this.url.saveUploadFile, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })

        if (response && response.success) {
          return {
            id: response.result.id || Date.now(),
            fileName: file.name,
            size: file.size,
            type: file.type,
            success: true
          }
        } else {
          throw new Error((response && response.message) || '上传失败')
        }
      } catch (error) {
        console.error(`文件 "${file.name}" 上传失败:`, error)
        throw error
      }
    },

    // 同步更新消息中的文件ID，确保与服务器中的文件ID一致
    syncMessageFileIds (uploadingMessages) {
      uploadingMessages.forEach(({ message, file }) => {
        // 只处理上传成功的文件
        if (message.fileInfo.status === 'uploaded') {
          // 在最新获取的文件列表中查找对应的文件
          const serverFile = this.uploadedFiles.find(
            (f) =>
              f.name === file.name &&
              Math.abs(new Date(f.uploadTime).getTime() - message.timestamp) <
                10000 // 10秒内上传的文件
          )

          if (serverFile) {
            // 更新消息中的文件ID为服务器返回的真实ID
            message.fileInfo.id = serverFile.id
            console.log(
              `同步文件ID: ${file.name}, 旧ID: ${message.fileInfo.id}, 新ID: ${serverFile.id}`
            )
          }
        }
      })
    },

    // 上传单个文件并更新对应的消息状态
    async uploadSingleFileWithMessage (file, message) {
      try {
        const formData = new FormData()
        formData.append('files', file)
        formData.append('userId', this.agent || 'default')

        const response = await postAction(this.url.saveUploadFile, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })

        if (response && response.success) {
          // 更新消息状态为上传成功
          message.fileInfo.status = 'uploaded'
          message.fileInfo.id = response.result.id || Date.now()
          message.content = `📎 ${file.name}`
          this.scrollToBottom()

          return {
            id: response.result.id || Date.now(),
            fileName: file.name,
            size: file.size,
            type: file.type,
            success: true
          }
        } else {
          throw new Error((response && response.message) || '上传失败')
        }
      } catch (error) {
        console.error(`文件 "${file.name}" 上传失败:`, error)

        // 更新消息状态为上传失败
        message.fileInfo.status = 'failed'
        message.content = `❌ ${file.name} (上传失败)`
        this.scrollToBottom()

        throw error
      }
    },

    // 处理文件操作
    handleFileAction (action, fileInfo) {
      this.selectedFileInfo = fileInfo
      this.fileActionModalVisible = true
    },

    // 处理引用文件
    handleReferenceFile (fileInfo) {
      if (!this.referencedFiles.find((f) => f.id === fileInfo.id)) {
        this.referencedFiles.push(fileInfo)
      }
      this.fileActionModalVisible = false
    },

    // 处理删除文件
    async handleDeleteFile (fileInfo) {
      try {
        const response = await deleteAction(this.url.deleteFile, {
          userId: this.agent || 'default',
          id: fileInfo.id
        })

        if (response && response.success) {
          // 从上传列表中移除
          this.uploadedFiles = this.uploadedFiles.filter(
            (f) => f.id !== fileInfo.id
          )

          // 从引用列表中移除
          this.referencedFiles = this.referencedFiles.filter(
            (f) => f.id !== fileInfo.id
          )

          // 从消息列表中移除对应的文件消息
          this.messages = this.messages.filter((message) => {
            if (!message.fileInfo) return true
            return message.fileInfo.id !== fileInfo.id
          })

          this.$refs.toast.show('文件删除成功', 'success')
        } else {
          throw new Error((response && response.message) || '删除失败')
        }
      } catch (error) {
        console.error('删除文件失败:', error)
        this.$refs.toast.show('删除文件失败', 'error')
      } finally {
        this.fileActionModalVisible = false
      }
    },

    // 移除引用
    handleRemoveReference (fileId) {
      this.referencedFiles = this.referencedFiles.filter(
        (f) => f.id !== fileId
      )
    },

    // 切换文件模式
    async handleToggleFileMode () {
      this.fileMode = !this.fileMode
      const modeText = this.fileMode ? '开启' : '关闭'

      // 先清空当前对话内容和文件
      await this.clearConversationAndFiles()

      this.$refs.toast.show(`已${modeText}个人知识库模式`, 'success')
    },

    // 清空对话内容和文件（用于切换模式）
    async clearConversationAndFiles () {
      try {
        // 停止当前的输出和打字机效果
        this.stopOutput = true
        this.isTyping = false
        this.clearTimer()
        this.clearTypewriterTimer()

        // 删除所有已上传的文件
        if (this.uploadedFiles.length > 0) {
          const deletePromises = this.uploadedFiles.map((file) =>
            deleteAction(this.url.deleteFile, {
              userId: this.agent || 'default',
              id: file.id
            }).catch((error) => {
              console.warn(`删除文件 ${file.name} 失败:`, error)
            })
          )

          // 等待所有文件删除完成
          await Promise.all(deletePromises)
        }

        // 清空所有相关状态
        this.messages = []
        this.referencedFiles = []
        this.uploadedFiles = []
        this.generateSessionId()

        // 显示新的欢迎消息
        this.showWelcomeMessage()

        console.log('切换模式时已清空对话内容和文件')
      } catch (error) {
        console.error('清空对话内容和文件失败:', error)
        // 即使删除文件失败，也清空本地状态
        this.messages = []
        this.referencedFiles = []
        this.uploadedFiles = []
        this.generateSessionId()
        this.showWelcomeMessage()
      }
    },

    // 处理消息操作
    handleMessageAction (action, messageInfo) {
      console.log('Message action:', action, messageInfo)
    },

    // 清空消息
    async handleClearMessages () {
      try {
        // 删除所有已上传的文件
        const deletePromises = this.uploadedFiles.map((file) =>
          deleteAction(this.url.deleteFile, {
            userId: this.agent || 'default',
            id: file.id
          }).catch((error) => {
            console.warn(`删除文件 ${file.name} 失败:`, error)
          })
        )

        // 等待所有文件删除完成
        await Promise.all(deletePromises)

        // 清空所有相关状态
        this.messages = []
        this.referencedFiles = []
        this.uploadedFiles = []
        this.generateSessionId()
        this.showWelcomeMessage()

        this.$refs.toast.show('聊天记录和文件已清空', 'success')
      } catch (error) {
        console.error('清空聊天记录失败:', error)
        // 即使删除文件失败，也清空本地状态
        this.messages = []
        this.referencedFiles = []
        this.uploadedFiles = []
        this.generateSessionId()
        this.showWelcomeMessage()

        this.$refs.toast.show(
          '聊天记录已清空，但部分文件删除可能失败',
          'warning'
        )
      }
    },

    // 生成消息ID
    generateMessageId () {
      return (
        'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
      )
    },

    // 格式化时间
    formatTime (timestamp) {
      const date = new Date(timestamp)
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const messageDate = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate()
      )

      if (messageDate.getTime() === today.getTime()) {
        return date.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        })
      } else {
        return (
          date.toLocaleDateString('zh-CN', {
            month: '2-digit',
            day: '2-digit'
          }) +
          ' ' +
          date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
          })
        )
      }
    }
  }
}
</script>

<style scoped>
.h5-chat-container {
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  overflow: hidden;
  position: relative;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .h5-chat-container {
    height: 100dvh;
  }
}

/* 深色模式兼容 */
@media (prefers-color-scheme: dark) {
  .h5-chat-container {
    background-color: #f5f5f5 !important;
  }
}
</style>
