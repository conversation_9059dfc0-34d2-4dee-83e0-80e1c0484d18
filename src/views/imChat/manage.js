import axios from 'axios'

// 创建axios实例，配置代理和请求头
const apiClient = axios.create({
  baseURL: 'http://10.211.67.106',
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
    'X-Request-Path': '/airChinaHelp'
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 在请求头中添加airChinaHelp路径
    config.headers['X-Air-China-Help'] = '/airChinaHelp'
    console.log(
      '发送请求:',
      config.method?.toUpperCase(),
      config.url,
      config.data || config.params
    )
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    console.log('收到响应:', response.status, response.data)
    return response
  },
  (error) => {
    console.error(
      '响应错误:',
      error.response?.status,
      error.response?.data || error.message
    )
    return Promise.reject(error)
  }
)

/**
 * DELETE请求
 * @param {string} url - 请求URL
 * @param {object} data - 请求数据
 * @param {object} config - 额外配置
 * @returns {Promise}
 */
export const deleteAction = (url, data = {}, config = {}) => {
  return apiClient.delete(url, {
    data,
    ...config
  })
}

/**
 * GET请求
 * @param {string} url - 请求URL
 * @param {object} params - 查询参数
 * @param {object} config - 额外配置
 * @returns {Promise}
 */
export const getAction = (url, params = {}, config = {}) => {
  return apiClient.get(url, {
    params,
    ...config
  })
}

/**
 * POST请求
 * @param {string} url - 请求URL
 * @param {object} data - 请求数据
 * @param {object} config - 额外配置
 * @returns {Promise}
 */
export const postAction = (url, data = {}, config = {}) => {
  return apiClient.post(url, data, config)
}

/**
 * PUT请求
 * @param {string} url - 请求URL
 * @param {object} data - 请求数据
 * @param {object} config - 额外配置
 * @returns {Promise}
 */
export const putAction = (url, data = {}, config = {}) => {
  return apiClient.put(url, data, config)
}

/**
 * PATCH请求
 * @param {string} url - 请求URL
 * @param {object} data - 请求数据
 * @param {object} config - 额外配置
 * @returns {Promise}
 */
export const patchAction = (url, data = {}, config = {}) => {
  return apiClient.patch(url, data, config)
}

// 导出axios实例，供其他地方使用
export default apiClient
