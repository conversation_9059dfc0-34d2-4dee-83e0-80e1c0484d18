<template>
  <div class="chat-content" ref="chatContent">
    <div class="message-list">
      <!-- 消息项 -->
      <div
        v-for="message in messages"
        :key="message.id"
        :class="['message-item', `message-${message.type}`]"
      >
        <!-- AI消息 -->
        <div v-if="message.type === 'ai'" class="ai-message">
          <div class="message-avatar">
            <img :src="message.avatar" alt="AI" />
          </div>
          <div class="message-content">
            <div class="message-bubble">
              <AiMarkdown :content="message.content" :needFormat="true" />
              <div
                v-if="message.basedOnFiles && message.basedOnFiles.length > 0"
                class="based-on-files"
              >
                <span class="based-on-label"
                  >{{ getFileAnalysisLabel(message) }}：</span
                >
                <span
                  v-for="file in message.basedOnFiles"
                  :key="file.id"
                  class="file-tag"
                >
                  {{ file.name }}
                </span>
              </div>
            </div>
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
        </div>

        <!-- 用户消息 -->
        <div v-else-if="message.type === 'user'" class="user-message">
          <div class="message-content">
            <div class="message-bubble">
              <!-- 如果是文件消息，显示文件信息 -->
              <div
                v-if="message.fileInfo"
                class="file-message-content"
                @click="handleFileClick(message.fileInfo)"
                @touchstart="handleFileTouchStart(message.fileInfo)"
                @touchend="handleFileTouchEnd"
              >
                <div class="file-icon">
                  <svg
                    v-if="message.fileInfo.status === 'uploading'"
                    class="uploading-icon"
                    viewBox="0 0 24 24"
                  >
                    <circle cx="12" cy="12" r="3" fill="currentColor">
                      <animate
                        attributeName="r"
                        values="3;6;3"
                        dur="1s"
                        repeatCount="indefinite"
                      />
                      <animate
                        attributeName="opacity"
                        values="1;0.5;1"
                        dur="1s"
                        repeatCount="indefinite"
                      />
                    </circle>
                  </svg>
                  <svg
                    v-else-if="getFileIcon(message.fileInfo.type)"
                    class="file-type-icon"
                    viewBox="0 0 24 24"
                  >
                    <path
                      :d="getFileIcon(message.fileInfo.type)"
                      fill="currentColor"
                    />
                  </svg>
                  <svg v-else class="default-file-icon" viewBox="0 0 24 24">
                    <path
                      d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <div class="file-details">
                  <div class="message-text" :title="message.fileInfo.name">
                    {{ truncateFileName(message.fileInfo.name) }}
                  </div>
                  <div class="file-info" v-if="message.fileInfo.size">
                    <span class="file-size">{{
                      formatFileSize(message.fileInfo.size)
                    }}</span>
                    <span
                      class="file-status"
                      v-if="message.fileInfo.status !== 'uploaded'"
                      >{{ getFileStatusText(message.fileInfo.status) }}</span
                    >
                  </div>
                </div>
                <div
                  v-if="message.fileInfo.status === 'uploaded'"
                  class="file-actions-hint"
                >
                  <svg class="action-icon" viewBox="0 0 24 24">
                    <circle cx="12" cy="12" r="2" />
                    <circle cx="12" cy="5" r="2" />
                    <circle cx="12" cy="19" r="2" />
                  </svg>
                </div>
              </div>
              <!-- 普通文本消息 -->
              <div v-else>
                <div class="message-text">{{ message.content }}</div>
                <div
                  v-if="
                    message.referencedFiles &&
                    message.referencedFiles.length > 0
                  "
                  class="referenced-files"
                >
                  <span class="referenced-label">引用文件：</span>
                  <span
                    v-for="file in message.referencedFiles"
                    :key="file.id"
                    class="file-tag"
                  >
                    {{ file.name }}
                  </span>
                </div>
              </div>
            </div>
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
          <div class="message-avatar">
            <div class="user-avatar">我</div>
          </div>
        </div>

        <!-- 文件消息 -->
        <div v-else-if="message.type === 'file'" class="file-message">
          <div class="message-content">
            <div
              class="file-bubble"
              :class="{
                'file-uploading': message.fileInfo.status === 'uploading',
                'file-uploaded': message.fileInfo.status === 'uploaded',
                'file-failed': message.fileInfo.status === 'failed',
                'file-deleted': message.fileInfo.status === 'deleted',
              }"
              @click="handleFileClick(message.fileInfo)"
              @touchstart="handleFileTouchStart(message.fileInfo)"
              @touchend="handleFileTouchEnd"
            >
              <div class="file-icon">
                <svg
                  v-if="message.fileInfo.status === 'uploading'"
                  class="uploading-icon"
                  viewBox="0 0 24 24"
                >
                  <circle cx="12" cy="12" r="3" fill="currentColor">
                    <animate
                      attributeName="r"
                      values="3;6;3"
                      dur="1s"
                      repeatCount="indefinite"
                    />
                    <animate
                      attributeName="opacity"
                      values="1;0.5;1"
                      dur="1s"
                      repeatCount="indefinite"
                    />
                  </circle>
                </svg>
                <svg
                  v-else-if="getFileIcon(message.fileInfo.type)"
                  class="file-type-icon"
                  viewBox="0 0 24 24"
                >
                  <path
                    :d="getFileIcon(message.fileInfo.type)"
                    fill="currentColor"
                  />
                </svg>
                <svg v-else class="default-file-icon" viewBox="0 0 24 24">
                  <path
                    d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"
                    fill="currentColor"
                  />
                </svg>
              </div>
              <div class="file-details">
                <div class="file-name" :title="message.fileInfo.name">
                  {{ truncateFileName(message.fileInfo.name) }}
                </div>
                <div class="file-info">
                  <span class="file-size">{{
                    formatFileSize(message.fileInfo.size)
                  }}</span>
                  <span class="file-status">{{
                    getFileStatusText(message.fileInfo.status)
                  }}</span>
                </div>
              </div>
              <div
                v-if="message.fileInfo.status === 'uploaded'"
                class="file-actions-hint"
              >
                <svg class="action-icon" viewBox="0 0 24 24">
                  <circle cx="12" cy="12" r="2" />
                  <circle cx="12" cy="5" r="2" />
                  <circle cx="12" cy="19" r="2" />
                </svg>
              </div>
            </div>
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
        </div>
      </div>

      <!-- 打字中状态 -->
      <div v-if="isTyping" class="message-item message-ai">
        <div class="ai-message">
          <div class="message-avatar">
            <img src="@/assets/svg/ai-avatar.svg" alt="AI" />
          </div>
          <div class="message-content">
            <div class="message-bubble typing-bubble">
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import AiMarkdown from '@/components/im-markdown-viewer.vue'

export default {
  name: 'ChatContent',
  components: {
    AiMarkdown
  },
  props: {
    messages: {
      type: Array,
      default: () => []
    },
    isTyping: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      touchTimer: null,
      touchedFile: null
    }
  },
  watch: {
    messages: {
      handler () {
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      },
      deep: true
    },
    isTyping () {
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    }
  },
  methods: {
    // 滚动到底部
    scrollToBottom () {
      const chatContent = this.$refs.chatContent
      if (chatContent) {
        chatContent.scrollTop = chatContent.scrollHeight
      }
    },

    // 格式化消息内容
    formatMessageContent (content) {
      if (!content) return ''
      return content.replace(/\n/g, '<br>')
    },

    // 格式化时间
    formatTime (timestamp) {
      const date = new Date(timestamp)
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const messageDate = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate()
      )

      if (messageDate.getTime() === today.getTime()) {
        return date.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        })
      } else {
        return (
          date.toLocaleDateString('zh-CN', {
            month: '2-digit',
            day: '2-digit'
          }) +
          ' ' +
          date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
          })
        )
      }
    },

    // 格式化文件大小
    formatFileSize (bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    // 截断文件名 - 保留前后部分，中间用省略号
    truncateFileName (fileName) {
      if (!fileName) return ''

      // 移动端使用更短的长度
      const maxLength = 20

      if (fileName.length <= maxLength) return fileName

      // 获取文件名和扩展名
      const lastDotIndex = fileName.lastIndexOf('.')
      let name = fileName
      let extension = ''

      if (lastDotIndex !== -1) {
        name = fileName.substring(0, lastDotIndex)
        extension = fileName.substring(lastDotIndex)
      }

      // 如果只有扩展名过长，直接截断
      if (name.length === 0) {
        return fileName.substring(0, maxLength - 3) + '...'
      }

      // 为扩展名预留空间
      const extensionLength = extension.length
      const availableLength = maxLength - extensionLength - 3 // 3个字符的省略号

      if (availableLength <= 0) {
        return fileName.substring(0, maxLength - 3) + '...'
      }

      // 保留前面部分和后面的扩展名
      const frontLength = Math.max(1, availableLength)
      const truncatedName = name.substring(0, frontLength)

      return truncatedName + '...' + extension
    },

    // 获取文件状态文本
    getFileStatusText (status) {
      const statusMap = {
        uploading: '上传中...',
        uploaded: '已上传',
        failed: '上传失败',
        deleted: '已删除'
      }
      return statusMap[status] || ''
    },

    // 获取文件分析标签
    getFileAnalysisLabel (message) {
      if (!message.basedOnFiles || message.basedOnFiles.length === 0) {
        return '基于文件'
      }

      // 检查是否有 fileAnalysisType 标记
      if (message.fileAnalysisType) {
        switch (message.fileAnalysisType) {
          case 'referenced':
            return '基于引用文件'
          case 'all':
            return '基于已上传文件'
          default:
            return '基于文件'
        }
      }
    },

    // 获取文件图标
    getFileIcon (fileType) {
      if (fileType.includes('image')) {
        return 'M19,7V4H5V7H19M21,2A1,1 0 0,1 22,3V9A1,1 0 0,1 21,10H3A1,1 0 0,1 2,9V3A1,1 0 0,1 3,2H21M19,10.5V21.5H5V10.5H19M21,9A1,1 0 0,1 22,10V22A1,1 0 0,1 21,23H3A1,1 0 0,1 2,22V10A1,1 0 0,1 3,9H21Z'
      } else if (fileType.includes('pdf')) {
        return 'M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M9.5,11.5C9.5,12.33 8.83,13 8,13H7V15H5.5V9H8C8.83,9 9.5,9.67 9.5,10.5V11.5M14.5,13.5C14.5,14.33 13.83,15 13,15H10.5V9H13C13.83,9 14.5,9.67 14.5,10.5V13.5M18.5,10.5H17V11.5H18.5V13H17V15H15.5V9H18.5V10.5Z'
      } else if (fileType.includes('word') || fileType.includes('document')) {
        return 'M6,2A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2H6M6,4H13V9H18V20H6V4M8,12V14H16V12H8M8,16V18H13V16H8Z'
      } else if (
        fileType.includes('excel') ||
        fileType.includes('spreadsheet')
      ) {
        return 'M16,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V8L16,3M16,5.5L18.5,8H16V5.5M8,13H11V15H8V13M8,16H11V18H8V16M13,13H16V15H13V13M13,16H16V18H13V16Z'
      }
      return null
    },

    // 处理文件点击
    handleFileClick (fileInfo) {
      if (fileInfo.status === 'uploaded') {
        this.$emit('file-action', 'click', fileInfo)
      }
    },

    // 处理文件长按开始
    handleFileTouchStart (fileInfo) {
      if (fileInfo.status === 'uploaded') {
        this.touchedFile = fileInfo
        this.touchTimer = setTimeout(() => {
          this.$emit('file-action', 'longpress', fileInfo)
          this.touchTimer = null
        }, 800) // 800ms长按
      }
    },

    // 处理文件长按结束
    handleFileTouchEnd () {
      if (this.touchTimer) {
        clearTimeout(this.touchTimer)
        this.touchTimer = null
      }
    }
  },

  beforeDestroy () {
    if (this.touchTimer) {
      clearTimeout(this.touchTimer)
    }
  }
}
</script>

<style scoped>
.chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 12px;
  background: #f8f9fa;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.chat-content::-webkit-scrollbar {
  display: none;
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-height: 100%;
}

.message-item {
  width: 100%;
}

/* AI消息样式 */
.ai-message {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  max-width: 85%;
}

.message-avatar img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.ai-message .message-content {
  flex: 1;
}

.ai-message .message-bubble {
  background: white;
  border-radius: 16px 16px 16px 4px;
  padding: 12px 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  word-wrap: break-word;
  font-size: 14px !important;
}

.file-message-content {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
}

.file-message-content:active {
  transform: scale(0.98);
}

/* 用户消息中的文件图标 */
.user-message .file-message-content .file-icon {
  width: 32px;
  height: 32px;
  color: rgba(255, 255, 255, 0.9);
}

.user-message .file-message-content .file-type-icon,
.user-message .file-message-content .default-file-icon,
.user-message .file-message-content .uploading-icon {
  width: 20px;
  height: 20px;
}

/* 用户消息中的文件详情 */
.user-message .file-message-content .file-details {
  flex: 1;
  min-width: 0;
}

.user-message .file-message-content .message-text {
  color: white;
  font-size: 14px !important;
  font-weight: 500;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.user-message .file-message-content .file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
}

.user-message .file-message-content .file-size {
  color: rgba(255, 255, 255, 0.7);
}

.user-message .file-message-content .file-status {
  padding: 1px 4px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 10px;
}

/* 用户消息中的文件操作提示 */
.user-message .file-message-content .file-actions-hint {
  color: rgba(255, 255, 255, 0.6);
}

.user-message .file-message-content .action-icon {
  width: 14px;
  height: 14px;
}

/* 用户消息样式 */
.user-message {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  max-width: 85%;
  margin-left: auto;
  justify-content: flex-end;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
}

.user-message .message-content {
  flex: 1;
  text-align: right;
}

.user-message .message-bubble {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16px 16px 4px 16px;
  padding: 12px 16px;
  display: inline-block;
  text-align: left;
  position: relative;
  word-wrap: break-word;
  font-size: 14px !important;
}

/* 文件消息样式 */
.file-message {
  max-width: 85%;
}

.file-bubble {
  background: white;
  border-radius: 12px;
  padding: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 12px;
  user-select: none;
}

.file-bubble:active {
  transform: scale(0.98);
}

.file-bubble.file-uploading {
  border-color: #1890ff;
  background: #f6ffed;
}

.file-bubble.file-uploaded {
  border-color: #52c41a;
}

.file-bubble.file-uploaded:hover {
  border-color: #389e0d;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.file-bubble.file-failed {
  border-color: #ff4d4f;
  background: #fff2f0;
}

.file-bubble.file-deleted {
  border-color: #d9d9d9;
  background: #f5f5f5;
  opacity: 0.6;
  cursor: not-allowed;
}

.file-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1890ff;
  flex-shrink: 0;
}

.file-type-icon,
.default-file-icon,
.uploading-icon {
  width: 24px;
  height: 24px;
}

.uploading-icon {
  color: #1890ff;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #666;
}

.file-status {
  padding: 2px 6px;
  border-radius: 4px;
  background: #f0f0f0;
  font-size: 11px;
}

.file-bubble.file-uploading .file-status {
  background: #e6f7ff;
  color: #1890ff;
}

.file-bubble.file-uploaded .file-status {
  background: #f6ffed;
  color: #52c41a;
}

.file-bubble.file-failed .file-status {
  background: #fff2f0;
  color: #ff4d4f;
}

.file-actions-hint {
  color: #999;
  flex-shrink: 0;
}

.action-icon {
  width: 16px;
  height: 16px;
}

/* 消息时间样式 */
.message-time {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
  text-align: left;
}

.user-message .message-time {
  text-align: right;
}

/* 文件标签样式 */
.based-on-files,
.referenced-files {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  font-size: 12px;
}

.user-message .referenced-files {
  border-top-color: rgba(255, 255, 255, 0.3);
}

.based-on-label,
.referenced-label {
  color: #666;
  margin-right: 6px;
}

.user-message .referenced-label {
  color: rgba(255, 255, 255, 0.8);
}

.file-tag {
  display: inline-block;
  background: #f0f0f0;
  color: #333;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 11px;
  margin-right: 4px;
  margin-bottom: 2px;
}

.user-message .file-tag {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* 打字中指示器 */
.typing-bubble {
  background: white !important;
  padding: 16px !important;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  height: 16px;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #999;
  animation: typing-animation 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing-animation {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .chat-content {
    padding: 12px 8px;
  }

  .message-list {
    gap: 10px;
  }

  .ai-message,
  .user-message {
    max-width: 90%;
  }

  .message-avatar img,
  .user-avatar {
    width: 28px;
    height: 28px;
  }

  .ai-message .message-bubble,
  .user-message .message-bubble {
    padding: 10px 12px;
    border-radius: 3px 12px 12px 12px;
    font-size: 14px !important;
  }

  .user-message .message-bubble {
    border-radius: 12px 3px 12px 12px;
    font-size: 14px !important;
  }

  .file-bubble {
    padding: 10px;
    gap: 10px;
  }

  .file-icon {
    width: 36px;
    height: 36px;
  }

  .file-type-icon,
  .default-file-icon,
  .uploading-icon {
    width: 20px;
    height: 20px;
  }

  .file-name {
    font-size: 13px;
  }

  .file-info {
    font-size: 11px;
  }

  .message-time {
    font-size: 10px;
  }

  .based-on-files,
  .referenced-files {
    margin-top: 6px;
    padding-top: 6px;
    font-size: 11px;
  }

  .file-tag {
    font-size: 10px;
    padding: 1px 4px;
  }

  /* 移动端用户消息中的文件样式 */
  .user-message .file-message-content {
    gap: 10px;
  }

  .user-message .file-message-content .file-icon {
    width: 28px;
    height: 28px;
  }

  .user-message .file-message-content .file-type-icon,
  .user-message .file-message-content .default-file-icon,
  .user-message .file-message-content .uploading-icon {
    width: 18px;
    height: 18px;
  }

  .user-message .file-message-content .message-text {
    font-size: 13px !important;
    max-width: calc(100vw - 120px); /* 为头像和边距预留空间 */
  }

  .user-message .file-message-content .file-info {
    font-size: 10px;
  }

  .user-message .file-message-content .action-icon {
    width: 12px;
    height: 12px;
  }
}
</style>
