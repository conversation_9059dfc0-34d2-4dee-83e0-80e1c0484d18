<template>
  <div v-if="visible" class="h5-toast" :class="currentType">
    <div class="toast-content">
      <div class="toast-icon">
        <svg
          v-if="currentType === 'success'"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M20 6L9 17l-5-5"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
        <svg
          v-else-if="currentType === 'error'"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="2"
          />
          <line
            x1="15"
            y1="9"
            x2="9"
            y2="15"
            stroke="currentColor"
            stroke-width="2"
          />
          <line
            x1="9"
            y1="9"
            x2="15"
            y2="15"
            stroke="currentColor"
            stroke-width="2"
          />
        </svg>
        <svg
          v-else-if="currentType === 'warning'"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <line
            x1="12"
            y1="9"
            x2="12"
            y2="13"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <circle
            cx="12"
            cy="17"
            r="1"
            stroke="currentColor"
            stroke-width="2"
          />
        </svg>
        <svg
          v-else
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="2"
          />
          <path
            d="M12 16v-4M12 8h.01"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <span class="toast-text">{{ currentMessage }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ToastMessage',
  data () {
    return {
      visible: false,
      currentMessage: '',
      currentType: 'info',
      timer: null
    }
  },

  beforeDestroy () {
    this.clearTimer()
  },

  methods: {
    // 显示 Toast 消息
    show (message, type = 'info', duration = 3000) {
      this.clearTimer()

      this.currentMessage = message
      this.currentType = type
      this.visible = true

      if (duration > 0) {
        this.timer = setTimeout(() => {
          this.hide()
        }, duration)
      }
    },

    // 隐藏 Toast 消息
    hide () {
      this.visible = false
      this.clearTimer()
    },

    // 清除定时器
    clearTimer () {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
    }
  }
}
</script>

<style lang="less" scoped>
.h5-toast {
  position: fixed;
  top: 10%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  min-width: 280px;
  max-width: 80vw;
  transition: all 0.3s ease;
  animation: toastShow 0.3s ease forwards;
}

@keyframes toastShow {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  border-radius: 8px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.h5-toast.success .toast-content {
  background: rgba(82, 196, 26, 0.9);
}

.h5-toast.error .toast-content {
  background: rgba(245, 34, 45, 0.9);
}

.h5-toast.warning .toast-content {
  background: rgba(250, 173, 20, 0.9);
}

.h5-toast.info .toast-content {
  background: rgba(24, 144, 255, 0.9);
}

.toast-icon {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toast-icon svg {
  width: 100%;
  height: 100%;
  display: block;
}

.toast-text {
  font-size: 14px;
  color: #fff;
  text-align: center;
  line-height: 1.4;
  word-break: break-word;
}
</style>
