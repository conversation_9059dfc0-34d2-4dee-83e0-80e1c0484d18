<template>
  <div class="chat-input">
    <!-- 引用文件区域 -->
    <div v-if="referencedFiles.length > 0" class="referenced-files-area">
      <div class="referenced-files-header">
        <span class="referenced-title"
          >引用文件（{{ referencedFiles.length }}）</span
        >
        <button class="clear-references-btn" @click="clearAllReferences">
          <svg class="icon" viewBox="0 0 24 24">
            <path
              d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"
              fill="currentColor"
            />
          </svg>
        </button>
      </div>
      <div class="referenced-files-list">
        <div
          v-for="file in referencedFiles"
          :key="file.id"
          class="referenced-file-item"
        >
          <div class="file-icon">
            <svg viewBox="0 0 24 24">
              <path :d="getFileIcon(file.type)" fill="currentColor" />
            </svg>
          </div>
          <span class="file-name">{{ file.name }}</span>
          <button class="remove-file-btn" @click="removeReference(file.id)">
            <svg class="icon" viewBox="0 0 24 24">
              <path
                d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"
                fill="currentColor"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-area">
      <!-- 输入框容器 -->
      <div class="input-container">
        <div class="input-wrapper">
          <textarea
            ref="textInput"
            v-model="inputText"
            class="text-input"
            placeholder="输入消息..."
            rows="1"
            @keyup.enter="handleEnterKey"
            @input="adjustTextareaHeight"
            @focus="handleInputFocus"
            @blur="handleInputBlur"
            :disabled="isTyping"
          ></textarea>

          <!-- 右侧按钮组 -->
          <div class="right-buttons">
            <!-- 文件模式开关 -->
            <button
              class="tool-btn file-mode-btn"
              @click="toggleFileMode"
              :class="{ active: fileMode }"
            >
              <svg class="icon" viewBox="0 0 24 24">
                <!-- 文档图标 -->
                <path
                  d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"
                  fill="currentColor"
                />
                <!-- 激活状态的勾选标记 -->
                <circle v-if="fileMode" cx="17" cy="7" r="3" fill="#4CAF50" />
                <path
                  v-if="fileMode"
                  d="M15.5,7L16.5,8L18.5,6"
                  stroke="white"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  fill="none"
                />
              </svg>
              <span class="tool-label">个人知识库</span>
            </button>

            <!-- 表情按钮 -->
            <button
              class="tool-btn emoji-btn"
              @click="toggleEmojiPicker"
              :class="{ active: showEmojiPicker }"
            >
              <svg class="icon" viewBox="0 0 24 24">
                <circle
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="2"
                  fill="none"
                />
                <path
                  d="M8 14s1.5 2 4 2 4-2 4-2"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  fill="none"
                />
                <circle cx="9" cy="9" r="1" fill="currentColor" />
                <circle cx="15" cy="9" r="1" fill="currentColor" />
              </svg>
            </button>

            <!-- 发送按钮或更多按钮 -->
            <button
              v-if="inputText.trim()"
              class="send-btn active"
              @click="sendMessage"
              :disabled="isTyping"
            >
              <svg class="send-icon" viewBox="0 0 24 24">
                <path
                  d="M2,21L23,12L2,3V10L17,12L2,14V21Z"
                  fill="currentColor"
                />
              </svg>
            </button>
            <button
              v-else
              class="tool-btn more-btn"
              @click="toggleMoreTools"
              :class="{ active: showExpandedTools }"
            >
              <svg
                class="icon plus-icon"
                viewBox="0 0 24 24"
                :class="{ rotated: showExpandedTools }"
              >
                <path
                  d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M13,7H11V11H7V13H11V17H13V13H17V11H13V7Z"
                  fill="currentColor"
                />
              </svg>
            </button>
          </div>
        </div>

        <!-- 扩展工具 -->
        <div class="expanded-tools" v-show="showExpandedTools">
          <button class="tool-btn voice-btn" @click="handleVoiceInput" disabled>
            <svg class="icon" viewBox="0 0 24 24">
              <path
                d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"
                fill="currentColor"
              />
            </svg>
            <span class="tool-label">语音</span>
          </button>

          <button class="tool-btn file-btn" @click="selectFile">
            <svg class="icon" viewBox="0 0 24 24">
              <path
                d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20M12,19L8,15H10.5V12H13.5V15H16L12,19Z"
                fill="currentColor"
              />
            </svg>
            <span class="tool-label">文件</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 表情选择器 -->
    <div
      v-if="showEmojiPicker"
      class="emoji-picker-overlay"
      @click="closeEmojiPicker"
    >
      <div class="emoji-picker" @click.stop>
        <div class="emoji-header">
          <span class="emoji-title">选择表情</span>
          <button class="close-btn" @click="closeEmojiPicker">
            <svg class="icon" viewBox="0 0 24 24">
              <path
                d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"
                fill="currentColor"
              />
            </svg>
          </button>
        </div>
        <div class="emoji-content">
          <div class="emoji-grid">
            <button
              v-for="emojiItem in emojiList"
              :key="emojiItem.id"
              class="emoji-item"
              :title="emojiItem.name"
              @click="insertEmoji(emojiItem.emoji)"
            >
              {{ emojiItem.emoji }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      style="display: none"
      accept=".pdf,.doc,.docx,.txt,.png,.jpg,.jpeg"
      multiple
      @change="handleFileSelect"
    />
  </div>
</template>

<script>
export default {
  name: 'ChatInput',
  props: {
    value: {
      type: String,
      default: ''
    },
    isTyping: {
      type: Boolean,
      default: false
    },
    uploadedFiles: {
      type: Array,
      default: () => []
    },
    referencedFiles: {
      type: Array,
      default: () => []
    },
    fileMode: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      inputText: '',
      showEmojiPicker: false,
      showExpandedTools: false,
      isInputFocused: false,

      // 表情列表
      emojiList: [
        { id: 'smile', emoji: '😀', name: '微笑' },
        { id: 'grin', emoji: '😃', name: '露齿笑' },
        { id: 'joy', emoji: '😄', name: '开心' },
        { id: 'laugh', emoji: '😁', name: '大笑' },
        { id: 'sweat_smile', emoji: '😆', name: '笑出汗' },
        { id: 'smile_sweat', emoji: '😅', name: '冷汗笑' },
        { id: 'laughing', emoji: '😂', name: '笑哭' },
        { id: 'rolling_laughing', emoji: '🤣', name: '笑倒' },
        { id: 'blush', emoji: '😊', name: '脸红' },
        { id: 'innocent', emoji: '😇', name: '天使' },
        { id: 'slight_smile', emoji: '🙂', name: '淡笑' },
        { id: 'upside_down', emoji: '🙃', name: '倒脸' },
        { id: 'wink', emoji: '😉', name: '眨眼' },
        { id: 'relieved', emoji: '😌', name: '如释重负' },
        { id: 'heart_eyes', emoji: '😍', name: '爱心眼' },
        { id: 'smiling_face_hearts', emoji: '🥰', name: '含爱笑脸' },
        { id: 'kissing_heart', emoji: '😘', name: '飞吻' },
        { id: 'kissing', emoji: '😗', name: '接吻' },
        { id: 'kissing_smiling_eyes', emoji: '😙', name: '眯眼接吻' },
        { id: 'kissing_closed_eyes', emoji: '😚', name: '闭眼接吻' },
        { id: 'yum', emoji: '😋', name: '美味' },
        { id: 'stuck_out_tongue', emoji: '😛', name: '吐舌' },
        { id: 'stuck_out_tongue_winking_eye', emoji: '😝', name: '眨眼吐舌' },
        { id: 'stuck_out_tongue_closed_eyes', emoji: '😜', name: '闭眼吐舌' },
        { id: 'zany_face', emoji: '🤪', name: '疯狂脸' },
        { id: 'raised_eyebrow', emoji: '🤨', name: '挑眉' },
        { id: 'monocle_face', emoji: '🧐', name: '单片眼镜' },
        { id: 'nerd_face', emoji: '🤓', name: '书呆子' },
        { id: 'sunglasses', emoji: '😎', name: '太阳镜' },
        { id: 'star_struck', emoji: '🤩', name: '星星眼' },
        { id: 'partying_face', emoji: '🥳', name: '派对脸' },
        { id: 'smirk', emoji: '😏', name: '得意' },
        { id: 'unamused', emoji: '😒', name: '无趣' },
        { id: 'disappointed', emoji: '😞', name: '失望' },
        { id: 'pensive', emoji: '😔', name: '沉思' },
        { id: 'worried', emoji: '😟', name: '担心' },
        { id: 'confused', emoji: '😕', name: '困惑' },
        { id: 'slightly_frowning_face', emoji: '🙁', name: '微皱眉' },
        { id: 'frowning_face', emoji: '☹️', name: '皱眉' },
        { id: 'persevere', emoji: '😣', name: '坚持' },
        { id: 'confounded', emoji: '😖', name: '难受' },
        { id: 'tired_face', emoji: '😫', name: '疲惫' },
        { id: 'weary', emoji: '😩', name: '厌倦' },
        { id: 'pleading_face', emoji: '🥺', name: '恳求' },
        { id: 'cry', emoji: '😢', name: '哭泣' },
        { id: 'sob', emoji: '😭', name: '大哭' },
        { id: 'triumph', emoji: '😤', name: '生气' },
        { id: 'angry', emoji: '😠', name: '愤怒' },
        { id: 'rage', emoji: '😡', name: '狂怒' },
        { id: 'face_with_symbols_on_mouth', emoji: '🤬', name: '爆粗口' },
        { id: 'exploding_head', emoji: '🤯', name: '爆炸头' },
        { id: 'flushed', emoji: '😳', name: '脸红' },
        { id: 'hot_face', emoji: '🥵', name: '热脸' },
        { id: 'cold_face', emoji: '🥶', name: '冷脸' },
        { id: 'scream', emoji: '😱', name: '尖叫' },
        { id: 'fearful', emoji: '😨', name: '恐惧' },
        { id: 'cold_sweat', emoji: '😰', name: '冷汗' },
        { id: 'disappointed_relieved', emoji: '😥', name: '失望但如释重负' },
        { id: 'sweat', emoji: '😓', name: '汗' },
        { id: 'hugs', emoji: '🤗', name: '拥抱' },
        { id: 'thinking', emoji: '🤔', name: '思考' },
        { id: 'face_with_hand_over_mouth', emoji: '🤭', name: '捂嘴' },
        { id: 'shushing_face', emoji: '🤫', name: '嘘' },
        { id: 'zipper_mouth_face', emoji: '🤐', name: '拉链嘴' },
        { id: 'sleeping', emoji: '😴', name: '睡觉' },
        { id: 'sleepy', emoji: '😪', name: '困倦' },
        { id: 'dizzy_face', emoji: '😵', name: '晕' },
        { id: 'hushed', emoji: '😯', name: '安静' },
        { id: 'drooling_face', emoji: '🤤', name: '流口水' },
        { id: 'thumbsup', emoji: '👍', name: '赞' },
        { id: 'thumbsdown', emoji: '👎', name: '踩' },
        { id: 'ok_hand', emoji: '👌', name: 'OK' },
        { id: 'v', emoji: '✌️', name: '胜利' },
        { id: 'crossed_fingers', emoji: '🤞', name: '交叉手指' },
        { id: 'love_you_gesture', emoji: '🤟', name: '爱你手势' },
        { id: 'metal', emoji: '🤘', name: '摇滚' },
        { id: 'call_me_hand', emoji: '🤙', name: '打电话' },
        { id: 'point_left', emoji: '👈', name: '左指' },
        { id: 'point_right', emoji: '👉', name: '右指' },
        { id: 'point_up_2', emoji: '👆', name: '上指' },
        { id: 'middle_finger', emoji: '🖕', name: '中指' },
        { id: 'point_down', emoji: '👇', name: '下指' },
        { id: 'point_up', emoji: '☝️', name: '食指' },
        { id: 'wave', emoji: '👋', name: '招手' },
        { id: 'raised_back_of_hand', emoji: '🤚', name: '手背' },
        { id: 'raised_hand_with_fingers_splayed', emoji: '🖐️', name: '张开手' },
        { id: 'hand', emoji: '✋', name: '停止' },
        { id: 'vulcan_salute', emoji: '🖖', name: '星际迷航' },
        { id: 'clap', emoji: '👏', name: '鼓掌' },
        { id: 'raised_hands', emoji: '🙌', name: '举手' },
        { id: 'handshake', emoji: '🤝', name: '握手' },
        { id: 'pray', emoji: '🙏', name: '祈祷' },
        { id: 'sparkles', emoji: '✨', name: '闪亮' },
        { id: 'tada', emoji: '🎉', name: '庆祝' },
        { id: 'confetti_ball', emoji: '🎊', name: '彩球' },
        { id: 'sparkling_heart', emoji: '💖', name: '闪亮心' },
        { id: 'two_hearts', emoji: '💕', name: '双心' },
        { id: 'cupid', emoji: '💘', name: '丘比特' },
        { id: 'heartbeat', emoji: '💓', name: '心跳' },

        // 更多心形表情
        { id: 'heart', emoji: '❤️', name: '红心' },
        { id: 'orange_heart', emoji: '🧡', name: '橙心' },
        { id: 'yellow_heart', emoji: '💛', name: '黄心' },
        { id: 'green_heart', emoji: '💚', name: '绿心' },
        { id: 'blue_heart', emoji: '💙', name: '蓝心' },
        { id: 'purple_heart', emoji: '💜', name: '紫心' },
        { id: 'brown_heart', emoji: '🤎', name: '棕心' },
        { id: 'black_heart', emoji: '🖤', name: '黑心' },
        { id: 'white_heart', emoji: '🤍', name: '白心' },
        { id: 'broken_heart', emoji: '💔', name: '心碎' },

        // 动物表情
        { id: 'dog', emoji: '🐶', name: '小狗' },
        { id: 'cat', emoji: '🐱', name: '小猫' },
        { id: 'mouse', emoji: '🐭', name: '老鼠' },
        { id: 'hamster', emoji: '🐹', name: '仓鼠' },
        { id: 'rabbit', emoji: '🐰', name: '兔子' },
        { id: 'fox_face', emoji: '🦊', name: '狐狸' },
        { id: 'bear', emoji: '🐻', name: '熊' },
        { id: 'panda_face', emoji: '🐼', name: '熊猫' },
        { id: 'koala', emoji: '🐨', name: '考拉' },
        { id: 'tiger', emoji: '🐯', name: '老虎' },
        { id: 'lion_face', emoji: '🦁', name: '狮子' },
        { id: 'cow', emoji: '🐮', name: '牛' },
        { id: 'pig', emoji: '🐷', name: '猪' },
        { id: 'pig_nose', emoji: '🐽', name: '猪鼻' },
        { id: 'frog', emoji: '🐸', name: '青蛙' },
        { id: 'monkey_face', emoji: '🐵', name: '猴子' },
        { id: 'see_no_evil', emoji: '🙈', name: '非礼勿视' },
        { id: 'hear_no_evil', emoji: '🙉', name: '非礼勿听' },
        { id: 'speak_no_evil', emoji: '🙊', name: '非礼勿言' },
        { id: 'chicken', emoji: '🐔', name: '鸡' },
        { id: 'penguin', emoji: '🐧', name: '企鹅' },
        { id: 'bird', emoji: '🐦', name: '鸟' },
        { id: 'baby_chick', emoji: '🐤', name: '小鸡' },
        { id: 'hatching_chick', emoji: '🐣', name: '破壳小鸡' },
        { id: 'hatched_chick', emoji: '🐥', name: '刚出壳小鸡' },
        { id: 'duck', emoji: '🦆', name: '鸭子' },
        { id: 'eagle', emoji: '🦅', name: '老鹰' },
        { id: 'owl', emoji: '🦉', name: '猫头鹰' },
        { id: 'bee', emoji: '🐝', name: '蜜蜂' },
        { id: 'bug', emoji: '🐛', name: '虫子' },
        { id: 'butterfly', emoji: '🦋', name: '蝴蝶' },
        { id: 'snail', emoji: '🐌', name: '蜗牛' },
        { id: 'spider', emoji: '🕷️', name: '蜘蛛' },
        { id: 'unicorn_face', emoji: '🦄', name: '独角兽' },

        // 食物表情
        { id: 'apple', emoji: '🍎', name: '苹果' },
        { id: 'green_apple', emoji: '🍏', name: '青苹果' },
        { id: 'pear', emoji: '🍐', name: '梨' },
        { id: 'tangerine', emoji: '🍊', name: '橘子' },
        { id: 'lemon', emoji: '🍋', name: '柠檬' },
        { id: 'banana', emoji: '🍌', name: '香蕉' },
        { id: 'watermelon', emoji: '🍉', name: '西瓜' },
        { id: 'grapes', emoji: '🍇', name: '葡萄' },
        { id: 'strawberry', emoji: '🍓', name: '草莓' },
        { id: 'peach', emoji: '🍑', name: '桃子' },
        { id: 'cherries', emoji: '🍒', name: '樱桃' },
        { id: 'pineapple', emoji: '🍍', name: '菠萝' },
        { id: 'tomato', emoji: '🍅', name: '番茄' },
        { id: 'eggplant', emoji: '🍆', name: '茄子' },
        { id: 'hot_pepper', emoji: '🌶️', name: '辣椒' },
        { id: 'cucumber', emoji: '🥒', name: '黄瓜' },
        { id: 'carrot', emoji: '🥕', name: '胡萝卜' },
        { id: 'potato', emoji: '🥔', name: '土豆' },
        { id: 'avocado', emoji: '🥑', name: '牛油果' },
        { id: 'bread', emoji: '🍞', name: '面包' },
        { id: 'croissant', emoji: '🥐', name: '牛角包' },
        { id: 'baguette_bread', emoji: '🥖', name: '法棍' },
        { id: 'pancakes', emoji: '🥞', name: '煎饼' },
        { id: 'cheese_wedge', emoji: '🧀', name: '奶酪' },
        { id: 'meat_on_bone', emoji: '🍖', name: '带骨肉' },
        { id: 'poultry_leg', emoji: '🍗', name: '鸡腿' },
        { id: 'bacon', emoji: '🥓', name: '培根' },
        { id: 'hamburger', emoji: '🍔', name: '汉堡' },
        { id: 'fries', emoji: '🍟', name: '薯条' },
        { id: 'pizza', emoji: '🍕', name: '披萨' },
        { id: 'hotdog', emoji: '🌭', name: '热狗' },
        { id: 'taco', emoji: '🌮', name: '玉米饼' },
        { id: 'burrito', emoji: '🌯', name: '卷饼' },
        { id: 'egg', emoji: '🥚', name: '鸡蛋' },
        { id: 'fried_egg', emoji: '🍳', name: '煎蛋' },
        { id: 'shallow_pan_of_food', emoji: '🥘', name: '炖菜' },
        { id: 'stew', emoji: '🍲', name: '炖汤' },
        { id: 'green_salad', emoji: '🥗', name: '沙拉' },
        { id: 'popcorn', emoji: '🍿', name: '爆米花' },
        { id: 'canned_food', emoji: '🥫', name: '罐头' },
        { id: 'bento', emoji: '🍱', name: '便当' },
        { id: 'rice_cracker', emoji: '🍘', name: '米饼' },
        { id: 'rice_ball', emoji: '🍙', name: '饭团' },
        { id: 'rice', emoji: '🍚', name: '米饭' },
        { id: 'curry', emoji: '🍛', name: '咖喱' },
        { id: 'ramen', emoji: '🍜', name: '拉面' },
        { id: 'spaghetti', emoji: '🍝', name: '意面' },
        { id: 'sweet_potato', emoji: '🍠', name: '烤红薯' },
        { id: 'oden', emoji: '🍢', name: '关东煮' },
        { id: 'sushi', emoji: '🍣', name: '寿司' },
        { id: 'fried_shrimp', emoji: '🍤', name: '炸虾' },
        { id: 'fish_cake', emoji: '🍥', name: '鱼糕' },
        { id: 'dango', emoji: '🍡', name: '团子' },
        { id: 'shaved_ice', emoji: '🍧', name: '刨冰' },
        { id: 'ice_cream', emoji: '🍨', name: '冰淇淋' },
        { id: 'icecream', emoji: '🍦', name: '软冰淇淋' },
        { id: 'cake', emoji: '🎂', name: '生日蛋糕' },
        { id: 'birthday', emoji: '🧁', name: '纸杯蛋糕' },
        { id: 'pie', emoji: '🥧', name: '派' },
        { id: 'chocolate_bar', emoji: '🍫', name: '巧克力' },
        { id: 'candy', emoji: '🍬', name: '糖果' },
        { id: 'lollipop', emoji: '🍭', name: '棒棒糖' },
        { id: 'custard', emoji: '🍮', name: '布丁' },
        { id: 'honey_pot', emoji: '🍯', name: '蜂蜜' },

        // 饮品表情
        { id: 'milk_glass', emoji: '🥛', name: '牛奶' },
        { id: 'coffee', emoji: '☕', name: '咖啡' },
        { id: 'tea', emoji: '🍵', name: '茶' },
        { id: 'sake', emoji: '🍶', name: '清酒' },
        { id: 'champagne', emoji: '🍾', name: '香槟' },
        { id: 'wine_glass', emoji: '🍷', name: '红酒' },
        { id: 'cocktail', emoji: '🍸', name: '鸡尾酒' },
        { id: 'tropical_drink', emoji: '🍹', name: '热带饮品' },
        { id: 'beer', emoji: '🍺', name: '啤酒' },
        { id: 'beers', emoji: '🍻', name: '干杯' },
        { id: 'clinking_glasses', emoji: '🥂', name: '碰杯' },
        { id: 'tumbler_glass', emoji: '🥃', name: '威士忌' },
        { id: 'cup_with_straw', emoji: '🥤', name: '饮料杯' },
        { id: 'bubble_tea', emoji: '🧋', name: '珍珠奶茶' },

        // 活动和运动表情
        { id: 'soccer', emoji: '⚽', name: '足球' },
        { id: 'basketball', emoji: '🏀', name: '篮球' },
        { id: 'football', emoji: '🏈', name: '橄榄球' },
        { id: 'baseball', emoji: '⚾', name: '棒球' },
        { id: 'tennis', emoji: '🎾', name: '网球' },
        { id: 'volleyball', emoji: '🏐', name: '排球' },
        { id: 'rugby_football', emoji: '🏉', name: '英式橄榄球' },
        { id: 'ping_pong', emoji: '🏓', name: '乒乓球' },
        {
          id: 'badminton_racquet_and_shuttlecock',
          emoji: '🏸',
          name: '羽毛球'
        },
        { id: 'goal_net', emoji: '🥅', name: '球门' },
        { id: 'ice_hockey_stick_and_puck', emoji: '🏒', name: '冰球' },
        { id: 'field_hockey_stick_and_ball', emoji: '🏑', name: '曲棍球' },
        { id: 'lacrosse', emoji: '🥍', name: '长曲棍球' },
        { id: 'cricket_bat_and_ball', emoji: '🏏', name: '板球' },
        { id: 'golf', emoji: '⛳', name: '高尔夫' },
        { id: 'bow_and_arrow', emoji: '🏹', name: '弓箭' },
        { id: 'fishing_pole_and_fish', emoji: '🎣', name: '钓鱼' },
        { id: 'running_shirt_with_sash', emoji: '🎽', name: '跑步衫' },
        { id: 'ski', emoji: '🎿', name: '滑雪' },
        { id: 'sled', emoji: '🛷', name: '雪橇' },
        { id: 'ice_skate', emoji: '⛸️', name: '滑冰' },

        // 自然和天气表情
        { id: 'sunny', emoji: '☀️', name: '晴天' },
        { id: 'partly_sunny', emoji: '⛅', name: '多云' },
        { id: 'cloud', emoji: '☁️', name: '云' },
        { id: 'partly_sunny_rain', emoji: '🌦️', name: '阵雨' },
        { id: 'thunder_cloud_and_rain', emoji: '⛈️', name: '雷雨' },
        { id: 'lightning', emoji: '⚡', name: '闪电' },
        { id: 'fire', emoji: '🔥', name: '火' },
        { id: 'snowflake', emoji: '❄️', name: '雪花' },
        { id: 'snowman', emoji: '☃️', name: '雪人' },
        { id: 'snowman_without_snow', emoji: '⛄', name: '雪人' },
        { id: 'comet', emoji: '☄️', name: '彗星' },
        { id: 'umbrella', emoji: '☂️', name: '雨伞' },
        { id: 'umbrella_with_rain_drops', emoji: '☔', name: '下雨伞' },
        { id: 'droplet', emoji: '💧', name: '水滴' },
        { id: 'ocean', emoji: '🌊', name: '海浪' },
        { id: 'rainbow', emoji: '🌈', name: '彩虹' },
        { id: 'sun_with_face', emoji: '🌞', name: '太阳脸' },
        { id: 'full_moon_with_face', emoji: '🌝', name: '满月脸' },
        { id: 'new_moon_with_face', emoji: '🌚', name: '新月脸' },
        { id: 'first_quarter_moon_with_face', emoji: '🌛', name: '上弦月脸' },
        { id: 'last_quarter_moon_with_face', emoji: '🌜', name: '下弦月脸' },
        { id: 'crescent_moon', emoji: '🌙', name: '弯月' },
        { id: 'earth_americas', emoji: '🌎', name: '地球美洲' },
        { id: 'earth_africa', emoji: '🌍', name: '地球非洲' },
        { id: 'earth_asia', emoji: '🌏', name: '地球亚洲' },
        { id: 'new_moon', emoji: '🌑', name: '新月' },
        { id: 'waxing_crescent_moon', emoji: '🌒', name: '蛾眉月' },
        { id: 'first_quarter_moon', emoji: '🌓', name: '上弦月' },
        { id: 'waxing_gibbous_moon', emoji: '🌔', name: '盈凸月' },
        { id: 'full_moon', emoji: '🌕', name: '满月' },
        { id: 'waning_gibbous_moon', emoji: '🌖', name: '亏凸月' },
        { id: 'last_quarter_moon', emoji: '🌗', name: '下弦月' },
        { id: 'waning_crescent_moon', emoji: '🌘', name: '残月' },
        { id: 'star', emoji: '⭐', name: '星星' },
        { id: 'star2', emoji: '🌟', name: '闪亮星' },
        { id: 'dizzy', emoji: '💫', name: '眩晕' },
        { id: 'boom', emoji: '💥', name: '爆炸' },

        // 植物表情
        { id: 'seedling', emoji: '🌱', name: '幼苗' },
        { id: 'evergreen_tree', emoji: '🌲', name: '常青树' },
        { id: 'deciduous_tree', emoji: '🌳', name: '落叶树' },
        { id: 'palm_tree', emoji: '🌴', name: '棕榈树' },
        { id: 'cactus', emoji: '🌵', name: '仙人掌' },
        { id: 'tulip', emoji: '🌷', name: '郁金香' },
        { id: 'cherry_blossom', emoji: '🌸', name: '樱花' },
        { id: 'rose', emoji: '🌹', name: '玫瑰' },
        { id: 'hibiscus', emoji: '🌺', name: '芙蓉花' },
        { id: 'sunflower', emoji: '🌻', name: '向日葵' },
        { id: 'blossom', emoji: '🌼', name: '花朵' },
        { id: 'corn', emoji: '🌽', name: '玉米' },
        { id: 'ear_of_rice', emoji: '🌾', name: '稻穗' },
        { id: 'herb', emoji: '🌿', name: '草药' },
        { id: 'shamrock', emoji: '☘️', name: '三叶草' },
        { id: 'four_leaf_clover', emoji: '🍀', name: '四叶草' },
        { id: 'maple_leaf', emoji: '🍁', name: '枫叶' },
        { id: 'fallen_leaf', emoji: '🍂', name: '落叶' },
        { id: 'leaves', emoji: '🍃', name: '叶子' },

        // 交通工具表情
        { id: 'car', emoji: '🚗', name: '汽车' },
        { id: 'taxi', emoji: '🚕', name: '出租车' },
        { id: 'blue_car', emoji: '🚙', name: '蓝色汽车' },
        { id: 'bus', emoji: '🚌', name: '公交车' },
        { id: 'trolleybus', emoji: '🚎', name: '电车' },
        { id: 'racing_car', emoji: '🏎️', name: '赛车' },
        { id: 'police_car', emoji: '🚓', name: '警车' },
        { id: 'ambulance', emoji: '🚑', name: '救护车' },
        { id: 'fire_engine', emoji: '🚒', name: '消防车' },
        { id: 'minibus', emoji: '🚐', name: '小巴' },
        { id: 'truck', emoji: '🚚', name: '卡车' },
        { id: 'articulated_lorry', emoji: '🚛', name: '货车' },
        { id: 'tractor', emoji: '🚜', name: '拖拉机' },
        { id: 'racing_motorcycle', emoji: '🏍️', name: '摩托车' },
        { id: 'bike', emoji: '🚲', name: '自行车' },
        { id: 'scooter', emoji: '🛴', name: '滑板车' },
        { id: 'skateboard', emoji: '🛹', name: '滑板' },
        { id: 'busstop', emoji: '🚏', name: '公交站' },
        { id: 'motorway', emoji: '🛣️', name: '高速公路' },
        { id: 'railway_track', emoji: '🛤️', name: '铁轨' },
        { id: 'train', emoji: '🚋', name: '有轨电车' },
        { id: 'monorail', emoji: '🚝', name: '单轨列车' },
        { id: 'bullettrain_side', emoji: '🚄', name: '高铁' },
        { id: 'bullettrain_front', emoji: '🚅', name: '子弹头列车' },
        { id: 'train2', emoji: '🚆', name: '火车' },
        { id: 'metro', emoji: '🚇', name: '地铁' },
        { id: 'light_rail', emoji: '🚈', name: '轻轨' },
        { id: 'station', emoji: '🚉', name: '车站' },
        { id: 'helicopter', emoji: '🚁', name: '直升机' },
        { id: 'small_airplane', emoji: '🛩️', name: '小飞机' },
        { id: 'airplane', emoji: '✈️', name: '飞机' },
        { id: 'flight_departure', emoji: '🛫', name: '起飞' },
        { id: 'flight_arrival', emoji: '🛬', name: '降落' },
        { id: 'rocket', emoji: '🚀', name: '火箭' },
        { id: 'artificial_satellite', emoji: '🛰️', name: '卫星' },
        { id: 'seat', emoji: '💺', name: '座位' },
        { id: 'anchor', emoji: '⚓', name: '锚' },
        { id: 'sailboat', emoji: '⛵', name: '帆船' },
        { id: 'speedboat', emoji: '🚤', name: '快艇' },
        { id: 'passenger_ship', emoji: '🛳️', name: '客轮' },
        { id: 'ferry', emoji: '⛴️', name: '渡轮' },
        { id: 'motor_boat', emoji: '🛥️', name: '摩托艇' },
        { id: 'ship', emoji: '🚢', name: '轮船' },

        // 物品表情
        { id: 'watch', emoji: '⌚', name: '手表' },
        { id: 'iphone', emoji: '📱', name: '手机' },
        { id: 'calling', emoji: '📲', name: '来电' },
        { id: 'computer', emoji: '💻', name: '笔记本电脑' },
        { id: 'keyboard', emoji: '⌨️', name: '键盘' },
        { id: 'desktop_computer', emoji: '🖥️', name: '台式电脑' },
        { id: 'printer', emoji: '🖨️', name: '打印机' },
        { id: 'computer_mouse', emoji: '🖱️', name: '鼠标' },
        { id: 'trackball', emoji: '🖲️', name: '轨迹球' },
        { id: 'joystick', emoji: '🕹️', name: '游戏杆' },
        { id: 'compression', emoji: '🗜️', name: '压缩' },
        { id: 'minidisc', emoji: '💽', name: '迷你光盘' },
        { id: 'floppy_disk', emoji: '💾', name: '软盘' },
        { id: 'cd', emoji: '💿', name: 'CD' },
        { id: 'dvd', emoji: '📀', name: 'DVD' },
        { id: 'vhs', emoji: '📼', name: '录像带' },
        { id: 'camera', emoji: '📷', name: '相机' },
        { id: 'camera_with_flash', emoji: '📸', name: '闪光灯相机' },
        { id: 'video_camera', emoji: '📹', name: '摄像机' },
        { id: 'movie_camera', emoji: '🎥', name: '电影摄像机' },
        { id: 'film_projector', emoji: '📽️', name: '电影放映机' },
        { id: 'film_strip', emoji: '🎞️', name: '胶片' },
        { id: 'telephone_receiver', emoji: '📞', name: '电话' },
        { id: 'phone', emoji: '☎️', name: '座机' },
        { id: 'pager', emoji: '📟', name: '寻呼机' },
        { id: 'fax', emoji: '📠', name: '传真机' },
        { id: 'tv', emoji: '📺', name: '电视' },
        { id: 'radio', emoji: '📻', name: '收音机' },
        { id: 'studio_microphone', emoji: '🎙️', name: '录音室话筒' },
        { id: 'level_slider', emoji: '🎚️', name: '调音台' },
        { id: 'control_knobs', emoji: '🎛️', name: '控制旋钮' },
        { id: 'stopwatch', emoji: '⏱️', name: '秒表' },
        { id: 'timer_clock', emoji: '⏲️', name: '定时器' },
        { id: 'alarm_clock', emoji: '⏰', name: '闹钟' },
        { id: 'mantelpiece_clock', emoji: '🕰️', name: '壁炉钟' },
        { id: 'hourglass_flowing_sand', emoji: '⏳', name: '沙漏' },
        { id: 'hourglass', emoji: '⌛', name: '沙漏完' },

        // 符号表情
        { id: 'heart_exclamation', emoji: '❣️', name: '心形感叹号' },
        { id: 'revolving_hearts', emoji: '💞', name: '旋转心' },
        { id: 'heartpulse', emoji: '💗', name: '心脏跳动' },
        { id: 'gift_heart', emoji: '💝', name: '礼物心' },
        { id: 'zzz', emoji: '💤', name: '睡觉' },
        { id: 'anger', emoji: '💢', name: '愤怒符号' },
        { id: 'bomb', emoji: '💣', name: '炸弹' },
        { id: 'speech_balloon', emoji: '💬', name: '对话气泡' },
        { id: 'thought_balloon', emoji: '💭', name: '思考气泡' },
        { id: 'hole', emoji: '🕳️', name: '洞' },
        { id: 'man_dancing', emoji: '🕺', name: '男士跳舞' },
        { id: 'dancers', emoji: '👯', name: '双人舞' },
        { id: 'levitate', emoji: '🕴️', name: '悬浮' },
        { id: 'speaking_head_in_silhouette', emoji: '🗣️', name: '说话头像' },
        { id: 'bust_in_silhouette', emoji: '👤', name: '头像剪影' },
        { id: 'busts_in_silhouette', emoji: '👥', name: '多人头像' },
        { id: 'fencer', emoji: '🤺', name: '击剑手' },
        { id: 'horse_racing', emoji: '🏇', name: '赛马' },
        { id: 'skier', emoji: '⛷️', name: '滑雪者' },
        { id: 'snowboarder', emoji: '🏂', name: '滑雪板' },
        { id: 'golfer', emoji: '🏌️', name: '高尔夫球手' },
        { id: 'surfer', emoji: '🏄', name: '冲浪者' },
        { id: 'rowboat', emoji: '🚣', name: '划船' },
        { id: 'swimmer', emoji: '🏊', name: '游泳者' },
        { id: 'bouncing_ball_person', emoji: '⛹️', name: '运球' },
        { id: 'weight_lifter', emoji: '🏋️', name: '举重' },
        { id: 'bicyclist', emoji: '🚴', name: '骑行者' },
        { id: 'mountain_bicyclist', emoji: '🚵', name: '山地骑行' },
        { id: 'racing_car_driver', emoji: '🏎️', name: '赛车手' },
        { id: 'motorcycle_rider', emoji: '🏍️', name: '摩托车手' },
        { id: 'person_doing_cartwheel', emoji: '🤸', name: '侧手翻' },
        { id: 'wrestlers', emoji: '🤼', name: '摔跤手' },
        { id: 'water_polo', emoji: '🤽', name: '水球' },
        { id: 'handball', emoji: '🤾', name: '手球' },
        { id: 'juggling', emoji: '🤹', name: '杂耍' }
      ]
    }
  },
  watch: {
    value: {
      handler (newVal) {
        this.inputText = newVal
      },
      immediate: true
    },
    inputText (newVal) {
      this.$emit('input', newVal)
    },
    showExpandedTools (newVal) {
      if (!newVal) {
        // 工具栏收起时也关闭表情选择器
        this.showEmojiPicker = false
      }
    }
  },
  mounted () {
    this.adjustTextareaHeight()
  },
  methods: {
    // 发送消息
    sendMessage () {
      if (this.inputText.trim() || this.referencedFiles.length > 0) {
        this.$emit('send-message', this.inputText.trim())
        this.inputText = ''
        this.adjustTextareaHeight()
        this.closeAllPickers()
      }
    },

    // 处理回车键
    handleEnterKey (event) {
      if (!event.shiftKey) {
        event.preventDefault()
        this.sendMessage()
      }
    },

    // 自动调整文本框高度
    adjustTextareaHeight () {
      this.$nextTick(() => {
        const textarea = this.$refs.textInput
        if (textarea) {
          textarea.style.height = 'auto'
          const maxHeight = 120 // 最大高度
          const newHeight = Math.min(textarea.scrollHeight, maxHeight)
          textarea.style.height = newHeight + 'px'
        }
      })
    },

    // 输入框聚焦
    handleInputFocus () {
      this.isInputFocused = true
      this.closeAllPickers()
    },

    // 输入框失焦
    handleInputBlur () {
      setTimeout(() => {
        this.isInputFocused = false
      }, 200)
    },

    // 切换文件模式
    toggleFileMode () {
      this.$emit('toggle-file-mode')
    },

    // 切换表情选择器
    toggleEmojiPicker () {
      this.showEmojiPicker = !this.showEmojiPicker
      if (this.showEmojiPicker) {
        this.showExpandedTools = true
      }
    },

    // 关闭表情选择器
    closeEmojiPicker () {
      this.showEmojiPicker = false
    },

    // 插入表情
    insertEmoji (emoji) {
      const textarea = this.$refs.textInput
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const text = this.inputText

      this.inputText = text.substring(0, start) + emoji + text.substring(end)

      this.$nextTick(() => {
        textarea.focus()
        textarea.setSelectionRange(start + emoji.length, start + emoji.length)
        this.adjustTextareaHeight()
      })

      this.closeEmojiPicker()
    },

    // 切换更多工具
    toggleMoreTools () {
      this.showExpandedTools = !this.showExpandedTools
    },

    // 处理语音输入
    handleVoiceInput () {
      // 暂时不实现语音功能，只做UI展示
      this.$message.info('语音功能暂未开放')
    },

    // 选择文件
    selectFile () {
      this.$refs.fileInput.click()
      this.closeAllPickers()
    },

    // 处理文件选择
    async handleFileSelect (event) {
      const files = Array.from(event.target.files)
      if (files.length === 0) return

      for (const file of files) {
        // 检查文件大小
        if (file.size > 10 * 1024 * 1024) {
          this.$message.error(`文件 "${file.name}" 超过10MB，请选择更小的文件`)
          continue
        }

        try {
          await this.$emit('upload-file', file)
        } catch (error) {
          console.error('文件上传失败:', error)
        }
      }

      // 清空文件输入
      event.target.value = ''
    },

    // 获取文件图标
    getFileIcon (fileType) {
      if (fileType.includes('image')) {
        return 'M19,7V4H5V7H19M21,2A1,1 0 0,1 22,3V9A1,1 0 0,1 21,10H3A1,1 0 0,1 2,9V3A1,1 0 0,1 3,2H21M19,10.5V21.5H5V10.5H19M21,9A1,1 0 0,1 22,10V22A1,1 0 0,1 21,23H3A1,1 0 0,1 2,22V10A1,1 0 0,1 3,9H21Z'
      } else if (fileType.includes('pdf')) {
        return 'M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M9.5,11.5C9.5,12.33 8.83,13 8,13H7V15H5.5V9H8C8.83,9 9.5,9.67 9.5,10.5V11.5M14.5,13.5C14.5,14.33 13.83,15 13,15H10.5V9H13C13.83,9 14.5,9.67 14.5,10.5V13.5M18.5,10.5H17V11.5H18.5V13H17V15H15.5V9H18.5V10.5Z'
      } else if (fileType.includes('word') || fileType.includes('document')) {
        return 'M6,2A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2H6M6,4H13V9H18V20H6V4M8,12V14H16V12H8M8,16V18H13V16H8Z'
      }
      return 'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z'
    },

    // 移除引用文件
    removeReference (fileId) {
      this.$emit('remove-reference', fileId)
    },

    // 清空所有引用
    clearAllReferences () {
      this.referencedFiles.forEach((file) => {
        this.$emit('remove-reference', file.id)
      })
    },

    // 关闭所有选择器
    closeAllPickers () {
      this.showEmojiPicker = false
      this.showExpandedTools = false
    }
  }
}
</script>

<style scoped>
.chat-input {
  background: white;
  border-top: 1px solid #e8e8e8;
  position: relative;
  z-index: 50;
}

/* 引用文件区域 */
.referenced-files-area {
  padding: 12px 16px 0;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.referenced-files-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.referenced-title {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.clear-references-btn {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.clear-references-btn:hover {
  background: #e6f7ff;
  color: #1890ff;
}

.clear-references-btn .icon {
  width: 14px;
  height: 14px;
}

.referenced-files-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding-bottom: 8px;
}

.referenced-file-item {
  display: flex;
  align-items: center;
  gap: 6px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 16px;
  padding: 4px 8px 4px 6px;
  font-size: 12px;
  color: #333;
  transition: all 0.3s ease;
}

.referenced-file-item:hover {
  border-color: #1890ff;
  background: #f6ffed;
}

.referenced-file-item .file-icon {
  width: 16px;
  height: 16px;
  color: #1890ff;
  flex-shrink: 0;
}

.referenced-file-item .file-icon svg {
  width: 100%;
  height: 100%;
}

.referenced-file-item .file-name {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-file-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 2px;
  border-radius: 50%;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.remove-file-btn:hover {
  background: #ff4d4f;
  color: white;
}

.remove-file-btn .icon {
  width: 12px;
  height: 12px;
}

/* 输入区域 */
.input-area {
  padding: 12px 16px;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  background: #f8f9fa;
  border-radius: 20px;
  padding: 8px 12px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #1890ff;
  background: white;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.text-input {
  flex: 1;
  border: none;
  background: none;
  outline: none;
  resize: none;
  font-size: 14px !important;
  line-height: 1.4;
  color: #333;
  min-height: 20px;
  max-height: 120px;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.text-input::-webkit-scrollbar {
  display: none;
}

.text-input::placeholder {
  color: #999;
}

.text-input:disabled {
  color: #ccc;
  cursor: not-allowed;
}

/* 右侧按钮组 */
.right-buttons {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-left: 8px;
}

.send-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: #d9d9d9;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.send-btn.active {
  background: #1890ff;
  transform: scale(1);
}

.send-btn.active:hover {
  background: #40a9ff;
  transform: scale(1.05);
}

.send-btn.active:active {
  transform: scale(0.95);
}

.send-btn:disabled {
  background: #d9d9d9;
  cursor: not-allowed;
  transform: none;
}

.send-icon {
  width: 16px;
  height: 16px;
}

/* 扩展工具区域 */
.expanded-tools {
  display: flex;
  justify-content: center;
  gap: 24px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 12px;
  margin-top: 8px;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.tool-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.3s ease;
  user-select: none;
  flex-shrink: 0;
}

/* 右侧按钮组中的工具按钮样式 */
.right-buttons .tool-btn .icon {
  width: 20px;
  height: 20px;
}

/* 文件模式按钮中的图标特定大小 */
.file-mode-btn .icon {
  width: 16px;
  height: 16px;
}

/* 扩展工具区域中的按钮样式 */
.expanded-tools .tool-btn {
  flex-direction: column;
  width: auto;
  height: auto;
  padding: 8px;
  border-radius: 8px;
  gap: 4px;
}

.expanded-tools .tool-btn .icon {
  width: 24px;
  height: 24px;
}

.tool-btn:hover {
  background: #e6f7ff;
  color: #1890ff;
}

.tool-btn.active {
  background: #e6f7ff;
  color: #1890ff;
}

.tool-btn:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.tool-btn:disabled:hover {
  background: none;
  color: #ccc;
}

/* 文件模式按钮特定样式 */
.file-mode-btn {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  width: auto;
  height: 32px;
  padding: 6px 8px;
  border-radius: 16px;
  white-space: nowrap;
}

.file-mode-btn.active {
  background: #e6f7ff;
  color: #1890ff;
}

.file-mode-btn.active:hover {
  background: #d1e9ff;
}

.file-mode-btn:not(.active) {
  color: #999;
}

.file-mode-btn:not(.active):hover {
  background: #f5f5f5;
  color: #666;
}

.tool-btn .icon {
  width: 24px;
  height: 24px;
  transition: transform 0.3s ease;
}

.plus-icon.rotated {
  transform: rotate(45deg);
}

.tool-label {
  font-size: 11px;
  font-weight: 500;
}

/* 表情选择器 */
.emoji-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.emoji-picker {
  background: white;
  border-radius: 16px 16px 0 0;
  width: 100%;
  max-height: 60vh;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0);
  }
}

.emoji-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.emoji-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #f0f0f0;
  color: #333;
}

.close-btn .icon {
  width: 20px;
  height: 20px;
}

.emoji-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
  -webkit-overflow-scrolling: touch;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
  gap: 8px;
}

.emoji-item {
  background: none;
  border: none;
  font-size: 24px;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
}

.emoji-item:hover {
  background: #f0f0f0;
  transform: scale(1.1);
}

.emoji-item:active {
  transform: scale(0.95);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .chat-input {
    position: sticky;
    bottom: 0;
  }

  .referenced-files-area {
    padding: 10px 12px 0;
  }

  .referenced-files-list {
    gap: 6px;
  }

  .referenced-file-item {
    font-size: 11px;
    padding: 3px 6px 3px 5px;
  }

  .referenced-file-item .file-icon {
    width: 14px;
    height: 14px;
  }

  .referenced-file-item .file-name {
    max-width: 100px;
  }

  .input-area {
    padding: 10px 12px;
  }

  .input-wrapper {
    padding: 6px 10px;
  }

  .send-btn {
    width: 28px;
    height: 28px;
  }

  .send-icon {
    width: 14px;
    height: 14px;
  }

  .basic-tools {
    gap: 12px;
  }

  .expanded-tools {
    gap: 20px;
    padding: 6px;
  }

  .tool-btn {
    padding: 6px;
  }

  .tool-btn .icon {
    width: 20px;
    height: 20px;
  }

  /* 移动端文件模式按钮调整 */
  .file-mode-btn {
    padding: 4px 6px;
    height: 28px;
    gap: 3px;
  }

  .file-mode-btn .icon {
    width: 14px;
    height: 14px;
  }

  .tool-label {
    font-size: 10px;
  }

  .emoji-header {
    padding: 12px 16px;
  }

  .emoji-title {
    font-size: 14px;
  }

  .emoji-content {
    padding: 12px 16px;
  }

  .emoji-grid {
    grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
    gap: 6px;
  }

  .emoji-item {
    font-size: 20px;
    padding: 6px;
  }
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .chat-input {
    padding-bottom: max(0px, env(safe-area-inset-bottom));
  }

  @media (max-width: 768px) {
    .input-area {
      padding-bottom: max(10px, env(safe-area-inset-bottom));
    }
  }
}

/* 键盘弹起时的适配 */
@media (max-width: 768px) {
  .chat-input.keyboard-open {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    z-index: 100;
  }
}
</style>
