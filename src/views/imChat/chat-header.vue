<template>
  <div class="chat-header">
    <div class="header-left">
      <button class="back-btn" @click="goBack">
        <svg
          class="icon"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M19 12H5M12 19L5 12L12 5"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </div>

    <div class="header-center">
      <div class="bot-info">
        <div class="bot-details">
          <div class="bot-name">{{ botInfo.name }}</div>
        </div>
      </div>
    </div>

    <div class="header-right">
      <button
        class="clear-btn"
        @click="showClearDialog"
        :disabled="isClearing"
        title="清空聊天记录和文件"
      >
        <svg
          class="icon"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M3 6H5H21"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </div>

    <!-- 清空确认弹窗 -->
    <div
      v-if="showClearConfirm"
      class="clear-confirm-overlay"
      @click="hideClearDialog"
    >
      <div class="clear-confirm-dialog" @click.stop>
        <div class="dialog-content">
          <h3>清空聊天记录</h3>
          <p>确定要清空所有聊天记录和文件吗？此操作不可恢复。</p>
          <div class="dialog-actions">
            <button class="cancel-btn" @click="hideClearDialog">取消</button>
            <button
              class="confirm-btn"
              @click="confirmClear"
              :disabled="isClearing"
            >
              {{ isClearing ? "清空中..." : "确定" }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChatHeader',
  props: {
    botInfo: {
      type: Object,
      required: true,
      default: () => ({
        name: 'AI助手',
        avatar: ''
      })
    },
    fileMode: {
      type: Boolean,
      default: false
    },
    uploadedFilesCount: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      showClearConfirm: false,
      isClearing: false
    }
  },
  methods: {
    // 返回上一页
    goBack () {
      if (this.$router) {
        this.$router.go(-1)
      } else {
        window.history.back()
      }
    },

    // 显示清空确认对话框
    showClearDialog () {
      this.showClearConfirm = true
    },

    // 隐藏清空确认对话框
    hideClearDialog () {
      if (!this.isClearing) {
        this.showClearConfirm = false
      }
    },

    // 确认清空
    async confirmClear () {
      this.isClearing = true
      try {
        // 触发清空操作并等待完成
        this.$emit('clear-messages')
        // 给一个小延迟让操作完成
        await new Promise((resolve) => setTimeout(resolve, 500))
        this.showClearConfirm = false
      } catch (error) {
        console.error('清空失败:', error)
      } finally {
        this.isClearing = false
      }
    }
  }
}
</script>

<style scoped>
.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  z-index: 100;
  min-height: 60px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left,
.header-right {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 16px;
}

.back-btn,
.clear-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.back-btn:hover,
.clear-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.back-btn:active,
.clear-btn:active {
  transform: scale(0.95);
}

.clear-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.icon {
  width: 20px;
  height: 20px;
  stroke-width: 2.5;
}

.bot-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.bot-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  object-fit: cover;
  background: white;
}

.bot-details {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.bot-name {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 2px;
}

.conversation-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  opacity: 0.9;
}

.conversation-id {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
}

.online-status {
  position: relative;
  padding-left: 8px;
  font-size: 11px;
}

.online-status::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background: #4caf50;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
  }
  70% {
    box-shadow: 0 0 0 4px rgba(76, 175, 80, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
  }
}

/* 清空确认弹窗样式 */
.clear-confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.clear-confirm-dialog {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin: 20px;
  max-width: 320px;
  width: 100%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  transform: scale(1);
  animation: popIn 0.3s ease-out;
}

@keyframes popIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.dialog-content h3 {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.dialog-content p {
  margin: 0 0 24px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  text-align: center;
}

.dialog-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.cancel-btn:hover {
  background: #e0e0e0;
}

.confirm-btn {
  background: #ff4757;
  color: white;
}

.confirm-btn:hover {
  background: #ff3742;
}

.confirm-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .chat-header {
    padding: 10px 12px;
    min-height: 56px;
  }

  .header-center {
    margin: 0 12px;
  }

  .back-btn,
  .clear-btn {
    width: 36px;
    height: 36px;
  }

  .icon {
    width: 18px;
    height: 18px;
  }

  .bot-avatar {
    width: 36px;
    height: 36px;
  }

  .bot-name {
    font-size: 15px;
  }

  .conversation-status {
    font-size: 11px;
    gap: 6px;
  }

  .conversation-id {
    font-size: 9px;
    padding: 1px 4px;
  }

  .online-status {
    font-size: 10px;
    padding-left: 6px;
  }

  .online-status::before {
    width: 5px;
    height: 5px;
  }

  .clear-confirm-dialog {
    margin: 16px;
    padding: 20px;
  }

  .dialog-content h3 {
    font-size: 16px;
  }

  .dialog-content p {
    font-size: 13px;
  }

  .cancel-btn,
  .confirm-btn {
    padding: 10px 16px;
    font-size: 13px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 375px) {
  .chat-header {
    padding: 8px 10px;
  }

  .header-center {
    margin: 0 8px;
  }

  .bot-info {
    gap: 8px;
  }

  .bot-name {
    font-size: 14px;
  }

  .conversation-status {
    font-size: 10px;
  }
}

/* 状态栏适配 */
@supports (padding: max(0px)) {
  .chat-header {
    padding-top: max(12px, env(safe-area-inset-top));
  }

  @media (max-width: 768px) {
    .chat-header {
      padding-top: max(10px, env(safe-area-inset-top));
    }
  }
}
</style>
