<template>
  <div v-if="visible" class="file-action-modal-overlay" @click="closeModal">
    <div class="file-action-modal" @click.stop>
      <!-- 文件信息展示 -->
      <div class="file-info-section" v-if="fileInfo">
        <div class="file-preview">
          <div class="file-icon">
            <svg viewBox="0 0 24 24">
              <path :d="getFileIcon(fileInfo.type)" fill="currentColor" />
            </svg>
          </div>
          <div class="file-details">
            <div class="file-name">{{ fileInfo.name }}</div>
            <div class="file-meta">
              <span class="file-size">{{ formatFileSize(fileInfo.size) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-buttons">
        <button class="action-btn reference-btn" @click="handleReference">
          <svg class="btn-icon" viewBox="0 0 24 24">
            <path
              d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"
              fill="currentColor"
            />
          </svg>
          <div class="btn-content">
            <span class="btn-title">引用文件</span>
            <span class="btn-description">基于此文件回答问题</span>
          </div>
        </button>

        <button class="action-btn delete-btn" @click="showDeleteConfirm">
          <svg class="btn-icon" viewBox="0 0 24 24">
            <path
              d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"
              fill="currentColor"
            />
          </svg>
          <div class="btn-content">
            <span class="btn-title">删除文件</span>
            <span class="btn-description">从服务器永久删除</span>
          </div>
        </button>
      </div>

      <!-- 取消按钮 -->
      <div class="cancel-section">
        <button class="cancel-btn" @click="closeModal">取消</button>
      </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div
      v-if="showDeleteConfirmDialog"
      class="delete-confirm-overlay"
      @click="hideDeleteConfirm"
    >
      <div class="delete-confirm-dialog" @click.stop>
        <div class="confirm-icon">
          <svg viewBox="0 0 24 24">
            <path
              d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M13,13H16L12,17L8,13H11V9H13V13Z"
              fill="currentColor"
            />
          </svg>
        </div>
        <div class="confirm-content">
          <h3>确认删除</h3>
          <p>确定要删除文件 "{{ fileInfo && fileInfo.name }}" 吗？</p>
          <p class="warning-text">此操作不可恢复，文件将从服务器永久删除。</p>
        </div>
        <div class="confirm-actions">
          <button
            class="confirm-cancel-btn"
            @click="hideDeleteConfirm"
            :disabled="isDeleting"
          >
            取消
          </button>
          <button
            class="confirm-delete-btn"
            @click="handleDelete"
            :disabled="isDeleting"
          >
            {{ isDeleting ? "删除中..." : "确认删除" }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FileActionModal',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    fileInfo: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      showDeleteConfirmDialog: false,
      isDeleting: false
    }
  },
  computed: {
    visible: {
      get () {
        return this.value
      },
      set (val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    visible (newVal) {
      if (!newVal) {
        // 关闭主弹窗时也关闭删除确认弹窗
        this.showDeleteConfirmDialog = false
        this.isDeleting = false
      }
    }
  },
  methods: {
    // 关闭弹窗
    closeModal () {
      if (!this.isDeleting) {
        this.visible = false
      }
    },

    // 处理引用文件
    handleReference () {
      this.$emit('reference-file', this.fileInfo)
      this.closeModal()
    },

    // 显示删除确认
    showDeleteConfirm () {
      this.showDeleteConfirmDialog = true
    },

    // 隐藏删除确认
    hideDeleteConfirm () {
      if (!this.isDeleting) {
        this.showDeleteConfirmDialog = false
      }
    },

    // 处理删除文件
    async handleDelete () {
      this.isDeleting = true
      try {
        this.$emit('delete-file', this.fileInfo)
        this.showDeleteConfirmDialog = false
        this.closeModal()
      } catch (error) {
        console.error('删除文件失败:', error)
      } finally {
        this.isDeleting = false
      }
    },

    // 获取文件图标
    getFileIcon (fileType) {
      if (fileType.includes('image')) {
        return 'M19,7V4H5V7H19M21,2A1,1 0 0,1 22,3V9A1,1 0 0,1 21,10H3A1,1 0 0,1 2,9V3A1,1 0 0,1 3,2H21M19,10.5V21.5H5V10.5H19M21,9A1,1 0 0,1 22,10V22A1,1 0 0,1 21,23H3A1,1 0 0,1 2,22V10A1,1 0 0,1 3,9H21Z'
      } else if (fileType.includes('pdf')) {
        return 'M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M9.5,11.5C9.5,12.33 8.83,13 8,13H7V15H5.5V9H8C8.83,9 9.5,9.67 9.5,10.5V11.5M14.5,13.5C14.5,14.33 13.83,15 13,15H10.5V9H13C13.83,9 14.5,9.67 14.5,10.5V13.5M18.5,10.5H17V11.5H18.5V13H17V15H15.5V9H18.5V10.5Z'
      } else if (fileType.includes('word') || fileType.includes('document')) {
        return 'M6,2A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2H6M6,4H13V9H18V20H6V4M8,12V14H16V12H8M8,16V18H13V16H8Z'
      } else if (
        fileType.includes('excel') ||
        fileType.includes('spreadsheet')
      ) {
        return 'M16,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V8L16,3M16,5.5L18.5,8H16V5.5M8,13H11V15H8V13M8,16H11V18H8V16M13,13H16V15H13V13M13,16H16V18H13V16Z'
      }
      return 'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z'
    },

    // 格式化文件大小
    formatFileSize (bytes) {
      if (!bytes) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
  }
}
</script>

<style scoped>
.file-action-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.file-action-modal {
  background: white;
  border-radius: 16px 16px 0 0;
  width: 100%;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
  overflow: hidden;
}

@keyframes slideUp {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0);
  }
}

/* 文件信息区域 */
.file-info-section {
  padding: 24px 20px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.file-preview {
  display: flex;
  align-items: center;
  gap: 16px;
}

.file-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f7ff;
  border-radius: 12px;
  color: #1890ff;
  flex-shrink: 0;
}

.file-icon svg {
  width: 28px;
  height: 28px;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 13px;
  color: #666;
}

.file-size {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
}

/* 操作按钮区域 */
.action-buttons {
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.action-btn:hover {
  border-color: #1890ff;
  background: #f6ffed;
}

.action-btn:active {
  transform: scale(0.98);
}

.reference-btn:hover {
  border-color: #52c41a;
  background: #f6ffed;
}

.reference-btn .btn-icon {
  color: #52c41a;
}

.delete-btn:hover {
  border-color: #ff4d4f;
  background: #fff2f0;
}

.delete-btn .btn-icon {
  color: #ff4d4f;
}

.btn-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.btn-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.btn-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
}

.btn-description {
  font-size: 12px;
  color: #666;
  line-height: 1.3;
}

/* 取消按钮区域 */
.cancel-section {
  padding: 16px 20px 24px;
  border-top: 1px solid #f0f0f0;
}

.cancel-btn {
  width: 100%;
  padding: 14px;
  background: #f5f5f5;
  border: none;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: #e9ecef;
  color: #333;
}

.cancel-btn:active {
  transform: scale(0.98);
}

/* 删除确认弹窗 */
.delete-confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100;
  animation: fadeIn 0.3s ease-out;
}

.delete-confirm-dialog {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin: 20px;
  max-width: 320px;
  width: 100%;
  text-align: center;
  animation: popIn 0.3s ease-out;
}

@keyframes popIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.confirm-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 16px;
  color: #ff4d4f;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff2f0;
  border-radius: 50%;
}

.confirm-icon svg {
  width: 32px;
  height: 32px;
}

.confirm-content h3 {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.confirm-content p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.warning-text {
  color: #ff4d4f !important;
  font-size: 13px !important;
  margin-bottom: 24px !important;
}

.confirm-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.confirm-cancel-btn,
.confirm-delete-btn {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.confirm-cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-cancel-btn:hover {
  background: #e9ecef;
  color: #333;
}

.confirm-cancel-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.confirm-delete-btn {
  background: #ff4d4f;
  color: white;
}

.confirm-delete-btn:hover {
  background: #ff7875;
}

.confirm-delete-btn:disabled {
  background: #ffccc7;
  cursor: not-allowed;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .file-action-modal {
    max-height: 80vh;
  }

  .file-info-section {
    padding: 20px 16px 12px;
  }

  .file-preview {
    gap: 12px;
  }

  .file-icon {
    width: 44px;
    height: 44px;
  }

  .file-icon svg {
    width: 24px;
    height: 24px;
  }

  .file-name {
    font-size: 15px;
  }

  .file-meta {
    font-size: 12px;
    gap: 8px;
  }

  .action-buttons {
    padding: 12px 16px;
    gap: 10px;
  }

  .action-btn {
    gap: 12px;
    padding: 14px;
  }

  .btn-icon {
    width: 22px;
    height: 22px;
  }

  .btn-title {
    font-size: 14px;
  }

  .btn-description {
    font-size: 11px;
  }

  .cancel-section {
    padding: 12px 16px 20px;
  }

  .cancel-btn {
    padding: 12px;
    font-size: 14px;
  }

  .delete-confirm-dialog {
    margin: 16px;
    padding: 20px;
  }

  .confirm-icon {
    width: 56px;
    height: 56px;
    margin-bottom: 12px;
  }

  .confirm-icon svg {
    width: 28px;
    height: 28px;
  }

  .confirm-content h3 {
    font-size: 16px;
  }

  .confirm-content p {
    font-size: 13px;
  }

  .warning-text {
    font-size: 12px !important;
  }

  .confirm-actions {
    margin-top: 20px;
    gap: 10px;
  }

  .confirm-cancel-btn,
  .confirm-delete-btn {
    padding: 10px 14px;
    font-size: 13px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 375px) {
  .file-info-section {
    padding: 16px 12px 10px;
  }

  .action-buttons {
    padding: 10px 12px;
  }

  .action-btn {
    padding: 12px;
  }

  .cancel-section {
    padding: 10px 12px 16px;
  }

  .delete-confirm-dialog {
    margin: 12px;
    padding: 16px;
  }
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .file-action-modal {
    padding-bottom: max(0px, env(safe-area-inset-bottom));
  }

  @media (max-width: 768px) {
    .cancel-section {
      padding-bottom: max(20px, env(safe-area-inset-bottom));
    }
  }
}
</style>
