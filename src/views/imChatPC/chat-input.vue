<template>
  <div class="pc-chat-input">
    <!-- 引用文件显示区域 -->
    <div v-if="referencedFiles.length > 0" class="referenced-files-bar">
      <div class="referenced-files-header">
        <el-icon class="el-icon-paperclip"></el-icon>
        <span>已引用 {{ referencedFiles.length }} 个文件</span>
      </div>
      <div class="referenced-files-list">
        <div
          v-for="file in referencedFiles"
          :key="file.id"
          class="referenced-file-item"
        >
          <el-icon class="el-icon-document"></el-icon>
          <span class="file-name">{{ file.name }}</span>
          <el-button
            type="text"
            size="mini"
            icon="el-icon-close"
            @click="removeReference(file.id)"
            class="remove-btn"
          />
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-container">
      <!-- 左侧工具栏 -->
      <div class="input-toolbar">
        <!-- 文件上传 -->
        <el-tooltip content="上传文件" placement="top">
          <el-upload
            ref="fileUpload"
            :show-file-list="false"
            :before-upload="handleFileUpload"
            :disabled="isTyping"
            multiple
            accept=".txt,.doc,.docx,.pdf,.xls,.xlsx,.ppt,.pptx,.md"
          >
            <el-button
              type="text"
              icon="el-icon-paperclip"
              :disabled="isTyping"
              class="tool-btn"
            />
          </el-upload>
        </el-tooltip>

        <!-- 文件模式切换 -->
        <el-tooltip :content="fileMode ? '关闭个人知识库模式' : '开启个人知识库模式'" placement="top">
          <el-button
            type="text"
            :icon="fileMode ? 'el-icon-folder-opened' : 'el-icon-folder'"
            @click="toggleFileMode"
            :disabled="isTyping"
            :class="['tool-btn', 'mode-btn', { active: fileMode }]"
          />
        </el-tooltip>
      </div>

      <!-- 输入框 -->
      <div class="input-wrapper">
        <el-input
          ref="messageInput"
          v-model="inputValue"
          type="textarea"
          :rows="1"
          :autosize="{ minRows: 1, maxRows: 6 }"
          placeholder="输入您的问题..."
          :disabled="isTyping"
          @keydown.enter.exact="handleEnterKey"
          @keydown.enter.shift.exact="handleShiftEnter"
          class="message-input"
        />
        
        <!-- 发送按钮 -->
        <el-button
          type="primary"
          icon="el-icon-s-promotion"
          @click="sendMessage"
          :disabled="isTyping || (!inputValue.trim() && referencedFiles.length === 0)"
          :loading="isTyping"
          class="send-btn"
        >
          发送
        </el-button>
      </div>
    </div>

    <!-- 底部提示 -->
    <div class="input-footer">
      <div class="tips">
        <span class="tip-item">
          <kbd>Enter</kbd> 发送消息
        </span>
        <span class="tip-item">
          <kbd>Shift</kbd> + <kbd>Enter</kbd> 换行
        </span>
        <span v-if="fileMode" class="tip-item mode-tip">
          <el-icon class="el-icon-folder-opened"></el-icon>
          个人知识库模式已开启
        </span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PCChatInput',
  props: {
    value: {
      type: String,
      default: ''
    },
    isTyping: {
      type: Boolean,
      default: false
    },
    uploadedFiles: {
      type: Array,
      default: () => []
    },
    referencedFiles: {
      type: Array,
      default: () => []
    },
    fileMode: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      inputValue: this.value
    }
  },
  watch: {
    value (newVal) {
      this.inputValue = newVal
    },
    inputValue (newVal) {
      this.$emit('input', newVal)
    }
  },
  mounted () {
    // 自动聚焦输入框
    this.$nextTick(() => {
      if (this.$refs.messageInput) {
        this.$refs.messageInput.focus()
      }
    })
  },
  methods: {
    sendMessage () {
      if (this.isTyping) return
      
      const content = this.inputValue.trim()
      if (!content && this.referencedFiles.length === 0) {
        return
      }

      this.$emit('send-message', content)
      this.inputValue = ''
      
      // 重新聚焦输入框
      this.$nextTick(() => {
        if (this.$refs.messageInput) {
          this.$refs.messageInput.focus()
        }
      })
    },

    handleEnterKey (event) {
      if (event.shiftKey) {
        // Shift + Enter 换行，不阻止默认行为
        return
      }
      
      // 阻止默认的换行行为
      event.preventDefault()
      this.sendMessage()
    },

    handleShiftEnter (event) {
      // Shift + Enter 换行，允许默认行为
      // 这个方法主要用于明确处理，实际上不需要特殊处理
    },

    async handleFileUpload (file) {
      // 阻止默认上传行为
      this.$emit('upload-file', file)
      return false
    },

    removeReference (fileId) {
      this.$emit('remove-reference', fileId)
    },

    toggleFileMode () {
      this.$emit('toggle-file-mode')
    }
  }
}
</script>

<style scoped>
.pc-chat-input {
  background: #ffffff;
  border-top: 1px solid #e8eaec;
  padding: 16px 24px;
}

.referenced-files-bar {
  margin-bottom: 12px;
  padding: 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.referenced-files-header {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  margin-bottom: 8px;
}

.referenced-files-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.referenced-file-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 16px;
  font-size: 12px;
  color: #495057;
}

.referenced-file-item .file-name {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-btn {
  padding: 0;
  margin-left: 4px;
  color: #6c757d;
}

.remove-btn:hover {
  color: #dc3545;
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 12px;
}

.input-toolbar {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-shrink: 0;
}

.tool-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #6c757d;
  transition: all 0.3s ease;
}

.tool-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

.mode-btn.active {
  background: #e3f2fd;
  color: #1976d2;
}

.input-wrapper {
  flex: 1;
  display: flex;
  align-items: flex-end;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 24px;
  transition: border-color 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #667eea;
  background: #ffffff;
}

.message-input {
  flex: 1;
}

.message-input >>> .el-textarea__inner {
  border: none;
  background: transparent;
  resize: none;
  padding: 0;
  font-size: 14px;
  line-height: 1.5;
  color: #2c3e50;
}

.message-input >>> .el-textarea__inner:focus {
  box-shadow: none;
}

.message-input >>> .el-textarea__inner::placeholder {
  color: #adb5bd;
}

.send-btn {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.3s ease;
}

.send-btn:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.send-btn:disabled {
  background: #e9ecef;
  color: #adb5bd;
  cursor: not-allowed;
}

.input-footer {
  margin-top: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tips {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 12px;
  color: #6c757d;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.tip-item kbd {
  padding: 2px 6px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 11px;
  font-family: inherit;
}

.mode-tip {
  color: #1976d2;
  font-weight: 500;
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
  .pc-chat-input {
    background: #34495e;
    border-top-color: #4a5f7a;
  }
  
  .referenced-files-bar {
    background: #2c3e50;
    border-color: #4a5f7a;
  }
  
  .referenced-files-header {
    color: #bdc3c7;
  }
  
  .referenced-file-item {
    background: #34495e;
    border-color: #4a5f7a;
    color: #ecf0f1;
  }
  
  .tool-btn {
    color: #bdc3c7;
  }
  
  .tool-btn:hover {
    background: #4a5f7a;
    color: #ecf0f1;
  }
  
  .input-wrapper {
    background: #2c3e50;
    border-color: #4a5f7a;
  }
  
  .input-wrapper:focus-within {
    border-color: #667eea;
    background: #34495e;
  }
  
  .message-input >>> .el-textarea__inner {
    color: #ecf0f1;
  }
  
  .message-input >>> .el-textarea__inner::placeholder {
    color: #7f8c8d;
  }
  
  .tips {
    color: #bdc3c7;
  }
  
  .tip-item kbd {
    background: #4a5f7a;
    border-color: #5d6d7e;
    color: #ecf0f1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pc-chat-input {
    padding: 12px 16px;
  }
  
  .input-toolbar {
    flex-direction: row;
  }
  
  .tips {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
