<template>
  <div class="pc-chat-header">
    <div class="header-left">
      <div class="bot-info">
        <div class="bot-avatar">
          <img :src="botInfo.avatar" :alt="botInfo.name" />
        </div>
        <div class="bot-details">
          <div class="bot-name">{{ botInfo.name }}</div>
          <div class="bot-status">
            <span class="status-dot"></span>
            在线
          </div>
        </div>
      </div>
    </div>

    <div class="header-center">
      <div class="file-mode-info" v-if="fileMode">
        <el-icon class="el-icon-folder-opened"></el-icon>
        <span>个人知识库模式</span>
        <el-badge :value="uploadedFilesCount" v-if="uploadedFilesCount > 0" class="file-badge">
          <el-icon class="el-icon-document"></el-icon>
        </el-badge>
      </div>
      <div class="normal-mode-info" v-else>
        <el-icon class="el-icon-chat-dot-round"></el-icon>
        <span>普通对话模式</span>
      </div>
    </div>

    <div class="header-right">
      <el-tooltip content="清空聊天记录和文件" placement="bottom">
        <el-button
          type="text"
          class="clear-btn"
          @click="showClearDialog"
          :disabled="isClearing"
          icon="el-icon-delete"
        >
          清空
        </el-button>
      </el-tooltip>
      
      <el-tooltip content="最小化" placement="bottom">
        <el-button
          type="text"
          class="minimize-btn"
          @click="handleMinimize"
          icon="el-icon-minus"
        >
        </el-button>
      </el-tooltip>
    </div>

    <!-- 清空确认对话框 -->
    <el-dialog
      title="确认清空"
      :visible.sync="clearDialogVisible"
      width="400px"
      center
    >
      <div class="clear-dialog-content">
        <el-icon class="el-icon-warning" style="color: #e6a23c; font-size: 24px;"></el-icon>
        <p>确定要清空所有聊天记录和上传的文件吗？</p>
        <p class="warning-text">此操作不可撤销</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="clearDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="confirmClear" :loading="isClearing">
          确定清空
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'PCChatHeader',
  props: {
    botInfo: {
      type: Object,
      default: () => ({
        name: 'AI助手',
        avatar: ''
      })
    },
    fileMode: {
      type: Boolean,
      default: true
    },
    uploadedFilesCount: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      clearDialogVisible: false,
      isClearing: false
    }
  },
  methods: {
    showClearDialog () {
      this.clearDialogVisible = true
    },
    
    async confirmClear () {
      this.isClearing = true
      try {
        this.$emit('clear-messages')
        this.clearDialogVisible = false
      } catch (error) {
        console.error('清空失败:', error)
      } finally {
        this.isClearing = false
      }
    },
    
    handleMinimize () {
      // PC端最小化功能，可以根据需要实现
      console.log('最小化窗口')
    }
  }
}
</script>

<style scoped>
.pc-chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: #ffffff;
  border-bottom: 1px solid #e8eaec;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
  flex: 0 0 auto;
}

.bot-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.bot-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #e8eaec;
}

.bot-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.bot-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.bot-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.2;
}

.bot-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #7f8c8d;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #27ae60;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(39, 174, 96, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(39, 174, 96, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(39, 174, 96, 0);
  }
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.file-mode-info,
.normal-mode-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.file-mode-info {
  background: #e8f5e8;
  color: #27ae60;
  border: 1px solid #27ae60;
}

.normal-mode-info {
  background: #e3f2fd;
  color: #2196f3;
  border: 1px solid #2196f3;
}

.file-badge {
  margin-left: 8px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 0 0 auto;
}

.clear-btn,
.minimize-btn {
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.clear-btn:hover {
  background: #fee;
  color: #e74c3c;
}

.minimize-btn:hover {
  background: #f5f5f5;
  color: #7f8c8d;
}

.clear-dialog-content {
  text-align: center;
  padding: 20px 0;
}

.clear-dialog-content p {
  margin: 12px 0 8px;
  font-size: 16px;
  color: #2c3e50;
}

.warning-text {
  font-size: 14px;
  color: #e6a23c;
  margin: 8px 0 0;
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
  .pc-chat-header {
    background: #34495e;
    border-bottom-color: #4a5f7a;
  }
  
  .bot-name {
    color: #ecf0f1;
  }
  
  .bot-status {
    color: #bdc3c7;
  }
  
  .bot-avatar {
    border-color: #4a5f7a;
  }
  
  .file-mode-info {
    background: rgba(39, 174, 96, 0.2);
    border-color: #27ae60;
  }
  
  .normal-mode-info {
    background: rgba(33, 150, 243, 0.2);
    border-color: #2196f3;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pc-chat-header {
    padding: 12px 16px;
  }
  
  .header-center {
    display: none;
  }
  
  .bot-name {
    font-size: 14px;
  }
  
  .clear-btn,
  .minimize-btn {
    padding: 6px 8px;
    font-size: 12px;
  }
}
</style>
