<template>
  <div class="pc-chat-content" ref="chatContent">
    <div class="messages-container">
      <!-- 消息列表 -->
      <div
        v-for="message in messages"
        :key="message.id"
        :class="['message-item', `message-${message.type}`]"
      >
        <!-- AI消息 -->
        <div v-if="message.type === 'ai'" class="ai-message">
          <div class="message-avatar">
            <img :src="message.avatar" alt="AI助手" />
          </div>
          <div class="message-content">
            <div class="message-bubble ai-bubble">
              <div v-if="message.isTypingCursor" class="typing-indicator">
                <span class="typing-text">{{ message.content }}</span>
                <span class="cursor">|</span>
              </div>
              <div v-else class="message-text" v-html="formatMessageContent(message.content)"></div>
            </div>
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
        </div>

        <!-- 用户消息 -->
        <div v-else-if="message.type === 'user'" class="user-message">
          <div class="message-content">
            <!-- 引用文件显示 -->
            <div v-if="message.referencedFiles && message.referencedFiles.length > 0" class="referenced-files">
              <div class="files-header">
                <el-icon class="el-icon-paperclip"></el-icon>
                <span>引用文件 ({{ message.referencedFiles.length }})</span>
              </div>
              <div class="files-list">
                <div
                  v-for="file in message.referencedFiles"
                  :key="file.id"
                  class="file-item"
                >
                  <el-icon class="el-icon-document"></el-icon>
                  <span class="file-name">{{ file.name }}</span>
                </div>
              </div>
            </div>
            
            <!-- 文件消息 -->
            <div v-if="message.fileInfo" class="file-message">
              <div class="file-content">
                <div class="file-icon">
                  <el-icon 
                    :class="getFileIcon(message.fileInfo.type)"
                    :style="{ color: getFileIconColor(message.fileInfo.status) }"
                  ></el-icon>
                </div>
                <div class="file-details">
                  <div class="file-name">{{ message.fileInfo.name }}</div>
                  <div class="file-meta">
                    <span class="file-size">{{ formatFileSize(message.fileInfo.size) }}</span>
                    <span class="file-status" :class="message.fileInfo.status">
                      {{ getFileStatusText(message.fileInfo.status) }}
                    </span>
                  </div>
                </div>
                <div class="file-actions" v-if="message.fileInfo.status === 'uploaded'">
                  <el-button
                    type="text"
                    size="mini"
                    @click="handleFileAction('preview', message.fileInfo)"
                  >
                    预览
                  </el-button>
                </div>
              </div>
            </div>
            
            <!-- 普通文本消息 -->
            <div v-if="message.content && !message.fileInfo" class="message-bubble user-bubble">
              <div class="message-text">{{ message.content }}</div>
            </div>
            
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
          <div class="message-avatar">
            <div class="user-avatar">
              <el-icon class="el-icon-user"></el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 正在输入指示器 -->
      <div v-if="isTyping" class="typing-indicator-container">
        <div class="ai-message">
          <div class="message-avatar">
            <img src="@/assets/svg/ai-avatar.svg" alt="AI助手" />
          </div>
          <div class="message-content">
            <div class="message-bubble ai-bubble typing-bubble">
              <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PCChatContent',
  props: {
    messages: {
      type: Array,
      default: () => []
    },
    isTyping: {
      type: Boolean,
      default: false
    }
  },
  mounted () {
    this.scrollToBottom()
  },
  updated () {
    this.scrollToBottom()
  },
  methods: {
    scrollToBottom () {
      this.$nextTick(() => {
        const container = this.$refs.chatContent
        if (container) {
          container.scrollTop = container.scrollHeight
        }
      })
    },

    formatMessageContent (content) {
      if (!content) return ''
      
      // 简单的换行处理
      return content.replace(/\n/g, '<br>')
    },

    formatTime (timestamp) {
      const date = new Date(timestamp)
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const messageDate = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate()
      )

      if (messageDate.getTime() === today.getTime()) {
        return date.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        })
      } else {
        return (
          date.toLocaleDateString('zh-CN', {
            month: '2-digit',
            day: '2-digit'
          }) +
          ' ' +
          date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
          })
        )
      }
    },

    formatFileSize (bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    getFileIcon (fileType) {
      if (!fileType) return 'el-icon-document'
      
      if (fileType.startsWith('image/')) return 'el-icon-picture'
      if (fileType.startsWith('video/')) return 'el-icon-video-camera'
      if (fileType.startsWith('audio/')) return 'el-icon-microphone'
      if (fileType.includes('pdf')) return 'el-icon-document'
      if (fileType.includes('word') || fileType.includes('doc')) return 'el-icon-document'
      if (fileType.includes('excel') || fileType.includes('sheet')) return 'el-icon-s-grid'
      if (fileType.includes('powerpoint') || fileType.includes('presentation')) return 'el-icon-s-marketing'
      
      return 'el-icon-document'
    },

    getFileIconColor (status) {
      switch (status) {
        case 'uploading': return '#409eff'
        case 'uploaded': return '#67c23a'
        case 'failed': return '#f56c6c'
        default: return '#909399'
      }
    },

    getFileStatusText (status) {
      switch (status) {
        case 'uploading': return '上传中...'
        case 'uploaded': return '已上传'
        case 'failed': return '上传失败'
        default: return ''
      }
    },

    handleFileAction (action, fileInfo) {
      this.$emit('file-action', action, fileInfo)
    }
  }
}
</script>

<style scoped>
.pc-chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #f8f9fa;
  position: relative;
}

.messages-container {
  max-width: 800px;
  margin: 0 auto;
  padding-bottom: 20px;
}

.message-item {
  margin-bottom: 20px;
}

.ai-message,
.user-message {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #e8eaec;
}

.message-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-avatar {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.message-content {
  flex: 1;
  max-width: 70%;
}

.user-message .message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
  position: relative;
  margin-bottom: 4px;
}

.ai-bubble {
  background: #ffffff;
  border: 1px solid #e8eaec;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.user-bubble {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.message-text {
  line-height: 1.5;
  font-size: 14px;
}

.message-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.user-message .message-time {
  text-align: right;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 2px;
}

.cursor {
  animation: blink 1s infinite;
  font-weight: bold;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.typing-bubble {
  padding: 16px;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #999;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.referenced-files {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
}

.files-header {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #0369a1;
  font-weight: 500;
  margin-bottom: 8px;
}

.files-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #0369a1;
}

.file-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-message {
  background: #ffffff;
  border: 1px solid #e8eaec;
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 8px;
}

.file-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-details .file-name {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.file-size {
  color: #7f8c8d;
}

.file-status {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.file-status.uploading {
  background: #e3f2fd;
  color: #1976d2;
}

.file-status.uploaded {
  background: #e8f5e8;
  color: #2e7d32;
}

.file-status.failed {
  background: #ffebee;
  color: #c62828;
}

.file-actions {
  flex-shrink: 0;
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
  .pc-chat-content {
    background: #2c3e50;
  }
  
  .ai-bubble {
    background: #34495e;
    border-color: #4a5f7a;
    color: #ecf0f1;
  }
  
  .message-time {
    color: #bdc3c7;
  }
  
  .referenced-files {
    background: rgba(3, 105, 161, 0.2);
    border-color: rgba(186, 230, 253, 0.3);
  }
  
  .file-message {
    background: #34495e;
    border-color: #4a5f7a;
  }
  
  .file-details .file-name {
    color: #ecf0f1;
  }
}

/* 滚动条样式 */
.pc-chat-content::-webkit-scrollbar {
  width: 6px;
}

.pc-chat-content::-webkit-scrollbar-track {
  background: transparent;
}

.pc-chat-content::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.pc-chat-content::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
