image: registry.roadtel.top/baseapp/docker:24.0.7

stages:
  - scan_compile_build

variables:
  DOCKER_TLS_CERTDIR: ''
  IMAGE: 'rdapp/poc-airchina-front'

before_script:
  - if [ ! "$REGISTRY_USER" ]||[ ! "$REGISTRY_PASS" ]||[ ! "$REGISTRY_HOST" ]; then echo "REGISTRY_* env no found, exit CI/CD. " && exit 1; fi
  - if [ ! "$HUB_USER" ]||[ ! "$HUB_PASS" ]||[ ! "$HUB_HOST" ]; then echo "HUB_* env no found, exit CI/CD. " && exit 1; fi
  - if [ ! "$SONAR_HOST_LOGIN" ]||[ ! "$SONAR_PROJECT" ]||[ ! "$SENDMAIL_PY" ]||[ ! "$MVN_SET_XML" ]; then echo "SONAR_* env no found, exit CI/CD. " && exit 1; fi
  - export lat_tag=:latest
  - if [ -n "$CI_COMMIT_TAG" ]; then tag=":$CI_COMMIT_TAG"; else tag=":$CI_COMMIT_SHORT_SHA"; fi && tag=$(echo $tag |tr '[:upper:]' '[:lower:]') && echo "Project build image tag $tag"
  - if [ $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH ]; then export sonar_branch="-Dsonar.branch.name=master" branch=master ; else branch=$CI_COMMIT_REF_NAME ; fi

scan_compile_build_image:
  image: registry.roadtel.top/baseapp/node16:1.0
  stage: scan_compile_build
  tags:
    - docker
  script:
    - pnpm install && pnpm build:stg
#    - npm install && npm build:stg
    - mkdir -p ci/dist && cp -rf dist/. ci/dist/ && pwd && ls -l
    ### 登录HUB服务器
    - docker login -u "$REGISTRY_USER" -p "$REGISTRY_PASS" $REGISTRY_HOST
    ### 构建容器镜像
    - cd ci && docker build --pull -t "$REGISTRY_HOST/$IMAGE${tag}" .
    - docker tag "$REGISTRY_HOST/$IMAGE${tag}" "$REGISTRY_HOST/$IMAGE${lat_tag}"
    - docker push "$REGISTRY_HOST/$IMAGE${tag}"
    - docker push "$REGISTRY_HOST/$IMAGE${lat_tag}"
