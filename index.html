<!DOCTYPE html>
<html>

<head>
  <meta charset=utf-8>
  <meta http-equiv=X-UA-Compatible content="IE=edge">
  <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0,viewport-fit=cover" name="viewport" />
  <meta content="yes" name="apple-mobile-web-app-capable" />
	<meta content="black" name="apple-mobile-web-app-status-bar-style" />
  <!-- <title>Chat</title> -->
</head>

<body>
  <script>
    (function() {
      // 检测是否为PC端
      function isPc() {
        const userAgentInfo = navigator.userAgent;
        const Agents = ['Android', 'iPhone', 'SymbianOS', 'Windows Phone', 'iPad', 'iPod'];
        let flag = true;
        for (let v = 0; v < Agents.length; v++) {
          if (userAgentInfo.indexOf(Agents[v]) > 0) {
            flag = false;
            break;
          }
        }
        return flag;
      }
  
      // PC端固定rem基准值
      if (isPc()) {
        const html = document.documentElement;
        html.style.fontSize = '54px';
      }
    })();
  </script>
  <div id=app></div>
  <script src="lib/fastclick.js"></script>
  <!-- <% if(process.env.NODE_ENV === 'development') {%>
    <script src="lib/vconsole.min.js"></script>
    <script>
      var vConsole = new VConsole();
    </script>
  <%}%> -->
</body>

</html>