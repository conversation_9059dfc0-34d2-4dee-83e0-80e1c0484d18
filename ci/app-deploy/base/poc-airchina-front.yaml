apiVersion: v1
kind: Service
metadata:
  labels:
    io.kompose.service: poc-airchina-front
  name: poc-airchina-front
spec:
  type: NodePort
  ports:
    - name: "80"
      port: 80
      targetPort: 80
  selector:
    io.kompose.service: poc-airchina-front
status:
  loadBalancer: {}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    io.kompose.service: poc-airchina-front
  name: poc-airchina-front
spec:
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: poc-airchina-front
  strategy: {}
  template:
    metadata:
      labels:
        io.kompose.service: poc-airchina-front
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: io.kompose.service
                      operator: In
                      values:
                        - poc-airchina-front
                topologyKey: kubernetes.io/hostname
              weight: 50
      containers:
        - image: hub.cloudcc.site/rdapp/poc-airchina-front:latest
          name: poc-airchina-front
          ports:
            - containerPort: 80
              protocol: TCP
          resources:
            limits:
              cpu: "1"
              memory: "512Mi"
            requests:
              cpu: "0.1"
              memory: "64Mi"
          livenessProbe:
            tcpSocket:
              port: 80
            initialDelaySeconds: 10
            periodSeconds: 5
            successThreshold: 1
            failureThreshold: 6
            timeoutSeconds: 1
          readinessProbe:
            httpGet:
              path: /
              port: 80
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 5
            successThreshold: 1
            failureThreshold: 3
            timeoutSeconds: 1
      restartPolicy: Always
status: {}
