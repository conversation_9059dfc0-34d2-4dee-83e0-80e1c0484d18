apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: poc-airchina-front
  annotations:
    nginx.ingress.kubernetes.io/affinity: "cookie"
    nginx.ingress.kubernetes.io/session-cookie-name: "route"
    nginx.ingress.kubernetes.io/session-cookie-expires: "172800"
    nginx.ingress.kubernetes.io/session-cookie-max-age: "172800"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  ingressClassName: higress
  rules:
  - http:
      paths:
      - pathType: ImplementationSpecific
        path: /im-layout(/|$)(.*)
        backend:
          service:
            name: poc-airchina-front
            port:
              number: 80
