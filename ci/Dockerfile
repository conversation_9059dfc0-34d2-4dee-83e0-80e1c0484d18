ARG TENGINE_TAG=3.1.0-r8
FROM registry.roadtel.top/baseapp/tengine:${TENGINE_TAG}
#
ENV TZ=Asia/Shanghai LANG=en_US.utf8 API_GATEWAY=""

### 安装bash tzdata、修改时区
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories && \
    apk add --no-cache curl bash dumb-init envsubst&& \
    apk add --no-cache -U tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    mkdir -p /etc/nginx/templates

### 复制定制文件和前端项目
#COPY client-web-tengine.conf.template /etc/nginx/templates/
#COPY 90-custom-preconfig.sh /docker-entrypoint.d/
COPY dist/ /usr/share/nginx/html/

### 基础镜像已包含此操作
RUN set -e && \
    chmod 755 /docker-entrypoint.d/*.sh

WORKDIR /

#启动时运行nginx
ENTRYPOINT ["/usr/bin/dumb-init", "--", "/docker-entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]
#CMD ["gosu", "1001:1001", "/bin/bash", "-c", "nginx -g 'daemon off;'"]
