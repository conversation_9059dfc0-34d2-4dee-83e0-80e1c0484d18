{"name": "vue-im", "version": "0.1.0", "author": "Stone", "private": true, "description": "基于vue2.0的即时通信App", "scripts": {"serve": "vue-cli-service serve", "test": "vue-cli-service serve --mode testing", "build": "vue-cli-service build", "build:stg": "vue-cli-service build --mode staging", "lint": "vue-cli-service lint"}, "dependencies": {"amfe-flexible": "^2.2.1", "axios": "^0.19.2", "core-js": "^3.6.5", "element-ui": "^2.15.14", "lodash": "^4.17.19", "markdown-it": "^12.3.2", "md5": "^2.3.0", "moment": "^2.27.0", "socket.io-client": "^3.0.1", "ua-parser-js": "^2.0.0", "vant": "^2.9.4", "vue": "^2.6.11", "vue-router": "^3.3.4", "vuex": "^3.5.1", "vuex-persistedstate": "^3.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.4.0", "@vue/cli-plugin-eslint": "~4.4.0", "@vue/cli-service": "~4.4.0", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.13.0", "babel-plugin-lodash": "^3.3.4", "babel-plugin-transform-remove-console": "^6.9.4", "copy-webpack-plugin": "^6.0.3", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "less": "^3.12.2", "less-loader": "^6.2.0", "lodash-webpack-plugin": "^0.11.5", "postcss-pxtorem": "^5.1.1", "style-resources-loader": "^1.3.3", "vue-template-compiler": "^2.6.11", "webpack-bundle-analyzer": "^3.9.0"}}